package com.wulin.gmserver.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.password.NoOpPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.HttpStatusEntryPoint;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.preauth.AbstractPreAuthenticatedProcessingFilter;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationProvider;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(securedEnabled = true, prePostEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    @Autowired
    MyUserDetailsService userService;

    @Autowired
    public void configureGlobalSecurity(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userService);
    }

    @Override
    protected void configure(HttpSecurity http) throws Exception {
       http.addFilterBefore(authenticationFilter(), AbstractPreAuthenticatedProcessingFilter.class);
        // http.addFilterBefore(jsonAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
//        http.authorizeRequests().anyRequest().permitAll();
        http.authorizeRequests()
                .antMatchers("/","/index").permitAll()
                .antMatchers("/*.js").permitAll()
                .antMatchers("/*.css").permitAll()
                .antMatchers("/login").permitAll()
                .anyRequest().authenticated().and()
//                .formLogin().loginPage("/login").usernameParameter("username").passwordParameter("password").disable()
                .exceptionHandling().authenticationEntryPoint(new HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED))
                .and().csrf().disable();

    }

    @Bean
    public static PasswordEncoder passwordEncoder() {
        return NoOpPasswordEncoder.getInstance();
    }

    @Bean
    JsonUserNameAuthenticationFilter jsonAuthenticationFilter() throws Exception {
        JsonUserNameAuthenticationFilter filter = new JsonUserNameAuthenticationFilter();
        filter.setAuthenticationManager(authenticationManagerBean());
        filter.setAuthenticationSuccessHandler((request, response, authentication) -> {
            Map<String, String> map = new HashMap<>();
            map.put("status", "ok");
            map.put("currentAuthority", "admin");
            ObjectMapper mapper = new ObjectMapper();
            response.getWriter().write(mapper.writeValueAsString(map));
            response.getWriter().flush();
            response.getWriter().close();
        });
        filter.setAuthenticationFailureHandler((request, response, exception) -> {
            Map<String, String> map = new HashMap<>();
            map.put("status", "error");
            map.put("msg", exception.getMessage());
            ObjectMapper mapper = new ObjectMapper();
            response.getWriter().write(mapper.writeValueAsString(map));
            response.getWriter().flush();
            response.getWriter().close();
        });
        return filter;
    }

    @Bean
    RequestHeadAuthenticationFilter authenticationFilter() throws Exception {
        RequestHeadAuthenticationFilter filter = new RequestHeadAuthenticationFilter();
        filter.setAuthenticationManager(preAuthAuthenticationManager());
//        filter.setAuthenticationSuccessHandler((request, response, authentication) -> {
//            Map<String, String> map = new HashMap<>();
//            map.put("status", "ok");
//            map.put("currentAuthority", "admin");
//            ObjectMapper mapper = new ObjectMapper();
//            response.getWriter().write(mapper.writeValueAsString(map));
//            response.getWriter().flush();
//            response.getWriter().close();
//        });
        filter.setAuthenticationFailureHandler((request, response, exception) -> {
            response.sendRedirect("http://sso.oa.wanmei.net/PWForms");
        });
        return filter;
    }

    @Autowired
    PreUserDetailsService preUserDetailsService;

    @Bean
    protected AuthenticationManager preAuthAuthenticationManager() {

        PreAuthenticatedAuthenticationProvider preAuthProvider= new PreAuthenticatedAuthenticationProvider();
        preAuthProvider.setPreAuthenticatedUserDetailsService(preUserDetailsService);

        List<AuthenticationProvider> providers = new ArrayList<>();
        providers.add(preAuthProvider);

        ProviderManager authMan = new ProviderManager(providers);
        return authMan;
    }

}
