package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.gm.Server;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Document
public interface ServerDao extends CrudRepository<Server, String> {
    Optional<Server> findByServerIdAndWgsName(Integer integer, String wgsName);
    List<Server> findAllByWgsName(String wgsName);
    List<Server> findAllById(Iterable<String> serverIds);
}
