package com.wulin.gmserver.security;

import org.springframework.security.web.authentication.preauth.AbstractPreAuthenticatedProcessingFilter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class RequestHeadAuthenticationFilter extends AbstractPreAuthenticatedProcessingFilter {

    @Override
    protected Object getPreAuthenticatedPrincipal(HttpServletRequest request) {
        return request.getHeader("X-SSO-UID");
    }

    @Override
    protected Object getPreAuthenticatedCredentials(HttpServletRequest request) {
        String uri = request.getRequestURI();
        String loginName = request.getHeader("X-SSO-UID");
        String email = request.getHeader("X-SSO-MAIL");
        //String fullName = URLDecoder.decode(request.getHeader("X-SSO-CN"),"UTF-8");
        String ssoSign = request.getHeader("X-SSO-SIGN");
        String remoteip = request.getHeader("X-SSO-RSR-IP");
        String user_agent = request.getHeader("User-Agent");
//        String calc_md5 = Constant.gen_http_sign(uri, loginName, remoteip,
//                user_agent, Constant.SSO_KEY);
//        if (!calc_md5.equals(ssoSign)) {
//            System.out.print("calc_md5=" + calc_md5 + ",ssoSign=" + ssoSign);
////            response.sendRedirect("http://sso.oa.wanmei.net/PWForms ");
//            return null;
//        } else {
//            return "";
//        }

        return "";
    }
}
