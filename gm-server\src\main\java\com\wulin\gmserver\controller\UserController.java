package com.wulin.gmserver.controller;

import com.wulin.gmserver.dao.ParamFilterDao;
import com.wulin.gmserver.dao.SysRoleDao;
import com.wulin.gmserver.dao.UserDao;
import com.wulin.gmserver.domain.SysRole;
import com.wulin.gmserver.domain.User;
import com.wulin.gmserver.domain.paramfilter.AbstractParamFilter;
import com.wulin.gmserver.security.MyUserDetails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
public class UserController {
    @Autowired
    UserDao userDao;

    @Autowired
    SysRoleDao sysRoleDao;

    @Autowired
    ParamFilterDao paramFilterDao;

    @Autowired
    PasswordEncoder encoder;

    @RequestMapping("/currentUser")
    public Object currentUser() {
        MyUserDetails userDetails = (MyUserDetails) SecurityContextHolder.getContext()
                .getAuthentication()
                .getPrincipal();
        User user = userDao.findByUserName(userDetails.getUsername()).get();

        return user;
    }

    @PreAuthorize("hasRole('Admin')")
    @RequestMapping(value = "/users", method = RequestMethod.POST)
    public Object createOrUpdate(@RequestBody Map<String, String> map) {
        String userName = map.get("userName");
        String password = map.get("password");
        String roleId = map.get("roleId");
        if (StringUtils.isEmpty(userName))
            return null;
        if (userDao.findByUserName(userName).isPresent()) {
            User user = userDao.findByUserName(userName).get();
            user.setUserName(userName);
            if(!StringUtils.isEmpty(password)){
                user.setPassword(password);
            }
            SysRole role = sysRoleDao.findById(roleId).get();
            user.setRole(role);
            userDao.save(user);
            return user;
        }
        User user = new User();
        user.setUserName(userName);
        user.setPassword(password);
        SysRole role = sysRoleDao.findById(roleId).get();
        user.setRole(role);
        userDao.save(user);
        return user;
    }
}
