
package wgs.msg.gm;

import com.goldhuman.Common.Marshal.Marshal;
import com.goldhuman.Common.Marshal.OctetsStream;
import com.goldhuman.Common.Marshal.MarshalException;

public class Command implements Marshal {
	public java.util.LinkedList<wgs.msg.gm.Param> params;
	public java.lang.String name;
	public java.lang.String desc;
	public boolean withroleid;

	public Command() {
		params = new java.util.LinkedList<wgs.msg.gm.Param>();
		name = "";
		desc = "";
	}

	public Command(java.util.LinkedList<wgs.msg.gm.Param> _params_, java.lang.String _name_, java.lang.String _desc_, boolean _withroleid_) {
		this.params = _params_;
		this.name = _name_;
		this.desc = _desc_;
		this.withroleid = _withroleid_;
	}

	public final boolean _validator_() {
		for (wgs.msg.gm.Param _v_ : params)
			if (!_v_._validator_()) return false;
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		_os_.compact_uint32(params.size());
		for (wgs.msg.gm.Param _v_ : params) {
			_os_.marshal(_v_);
		}
		_os_.marshal(name, "UTF-16LE");
		_os_.marshal(desc, "UTF-16LE");
		_os_.marshal(withroleid);
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		for (int _size_ = _os_.uncompact_uint32(); _size_ > 0; --_size_) {
			wgs.msg.gm.Param _v_ = new wgs.msg.gm.Param();
			_v_.unmarshal(_os_);
			params.add(_v_);
		}
		name = _os_.unmarshal_String("UTF-16LE");
		desc = _os_.unmarshal_String("UTF-16LE");
		withroleid = _os_.unmarshal_boolean();
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof Command) {
			Command _o_ = (Command)_o1_;
			if (!params.equals(_o_.params)) return false;
			if (!name.equals(_o_.name)) return false;
			if (!desc.equals(_o_.desc)) return false;
			if (withroleid != _o_.withroleid) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += params.hashCode();
		_h_ += name.hashCode();
		_h_ += desc.hashCode();
		_h_ += withroleid ? 1231 : 1237;
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(params).append(",");
		_sb_.append("T").append(name.length()).append(",");
		_sb_.append("T").append(desc.length()).append(",");
		_sb_.append(withroleid).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

}

