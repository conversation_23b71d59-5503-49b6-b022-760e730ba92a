package com.wulin.gmserver.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.Set;

@Data
@Document(collection = "player_online_gift_info")
public class PlayerOnlineGiftInfo {
    @Id
    private String id;
    private PlayerOnlineGiftDetail detail;
    @JsonIgnore
    private byte[] csv;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    private String creator;
    private Set<Integer> severIds;
    private boolean deleted;
}
