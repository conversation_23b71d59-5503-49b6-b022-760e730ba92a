package com.wulin.gmserver.controller.gm;

import com.wulin.gmserver.controller.OperationController;
import com.wulin.gmserver.dao.RecordDao;
import com.wulin.gmserver.dao.PlayerOnlineGiftInfoDao;
import com.wulin.gmserver.dao.ServerDao;
import com.wulin.gmserver.dao.UserDao;
import com.wulin.gmserver.domain.PlayerOnlineGiftDetail;
import com.wulin.gmserver.domain.PlayerOnlineGiftInfo;
import com.wulin.gmserver.domain.gm.Server;
import com.wulin.gmserver.security.MyUserDetails;
import com.wulin.gmserver.service.CommandService;
import com.wulin.gmserver.xio.Rpc;
import com.wulin.gmserver.xio.WgsClient;
import gnet.GmCmdRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import xio.Creator;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/gm/modules")
public class PlayerOnlineGiftController {

    @Autowired
    CommandService commandService;
    @Autowired
    ServerDao serverDao;

    @Autowired
    RecordDao recordDao;
    @Autowired
    UserDao userDao;

    @Autowired
    PlayerOnlineGiftInfoDao playerOnlineGiftInfoDao;

    @RequestMapping(value = "/PlayerOnlineGift", consumes = "multipart/form-data", method = RequestMethod.POST)
    public Object playerOnlineGift(PlayerOnlineGiftDetail playerOnlineGiftDetail,
            @RequestPart("files") MultipartFile file) throws IOException {
        byte[] bytes = file.getBytes();
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
        try (BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {

            List<String> lines = bufferedReader.lines().collect(Collectors.toList());
            if (lines.size() <= 1) {
                return false;
            }
            lines.remove(0);
            HashMap<Integer, StringBuilder> map = new HashMap<>();
            for (String line : lines) {
                String[] params = line.split(",");
                if (params.length < 4) {
                    return false;
                }
                long roleId = Long.parseLong(params[0].trim());
                int serverId = Integer.parseInt(params[1].trim());
                String items = params[2].trim();
                String coins = params[3].trim();
                String paramToGm = String.valueOf(roleId) + "," + items + "," + coins;
                StringBuilder builder = map.get(serverId);
                if (builder == null) {
                    builder = new StringBuilder();
                    map.put(serverId, builder);
                }
                builder.append(" ").append(paramToGm);
            }
            PlayerOnlineGiftInfo playerOnlineGiftInfo = new PlayerOnlineGiftInfo();
            MyUserDetails userDetails = (MyUserDetails) SecurityContextHolder.getContext()
                    .getAuthentication()
                    .getPrincipal();
            playerOnlineGiftInfo.setCsv(file.getBytes());
            playerOnlineGiftInfo.setCreateTime(new Date());
            playerOnlineGiftInfo.setCreator(userDetails.getUsername());
            playerOnlineGiftInfo.setDetail(playerOnlineGiftDetail);
            playerOnlineGiftInfo.setSeverIds(map.keySet());
            playerOnlineGiftInfo.setDeleted(false);
            playerOnlineGiftInfoDao.save(playerOnlineGiftInfo);

            List<Server> serverList = serverDao.findAllByWgsName(playerOnlineGiftDetail.getWgs());
            List<Map<String, Object>> result = new ArrayList<>();
            for (Server server : serverList) {
                if (!map.containsKey(server.getServerId())) {
                    continue;
                }
                String cmdAndParam = " rolebatch onlinegifts \"" + playerOnlineGiftInfo.getId() + "\" \""
                        + playerOnlineGiftDetail.getTitle() + "\" \"" + playerOnlineGiftDetail.getContent() + "\" \""
                        + playerOnlineGiftDetail.getStartTime() + "\" \"" + playerOnlineGiftDetail.getEndTime()
                        + "\" \"" + playerOnlineGiftDetail.getMinVipLevel() + "\" \""
                        + playerOnlineGiftDetail.getMaxVipLevel() + "\" \"" + playerOnlineGiftDetail.getMinLevel()
                        + "\" \"" + playerOnlineGiftDetail.getMaxLevel() + "\" \""
                        + playerOnlineGiftDetail.getPlatform() + "\"" + map.get(server.getServerId());

                Creator creator = WgsClient.getInstance().getCreatorByType(playerOnlineGiftDetail.getWgs());
                String cmdline = "gsd executeGM " + server.getServerId() + cmdAndParam;
                long identifier = Rpc.nextIdentifier();
                GmCmdRequest gmCmdRequest = new GmCmdRequest();
                gmCmdRequest.identifier = identifier;
                gmCmdRequest.cmdline = cmdline;
                gmCmdRequest.gmaccount = "admin";
                Object r;
                try {
                    gnet.GmCmdResponse rpcResult = new Rpc(identifier, gmCmdRequest)
                            .sendAndWaitResult(WgsClient.getInstance().get(creator), ********);
                    r = rpcResult != null ? rpcResult.result : "执行超时";
                } catch (Rpc.SendFailException e) {
                    r = "服务器连接失败";
                }
                Map<String, Object> m = new HashMap<>();
                m.put("serverId", server.getServerId());
                m.put("result", r);
                result.add(m);
            }
            return result;
        }
    }

    @RequestMapping(value = "/PlayerOnlineGift", method = RequestMethod.GET)
    public Object getAll(@RequestParam(defaultValue = "1") int currentPage,
            @RequestParam(defaultValue = "10") int pageSize) {
        PageRequest pageRequest = PageRequest.of(currentPage - 1, pageSize, Sort.by(Sort.Direction.DESC, "createTime"));
        Page<PlayerOnlineGiftInfo> page = playerOnlineGiftInfoDao.findAll(pageRequest);
        OperationController.Pagination pagination = OperationController.Pagination.builder().pageSize(page.getSize())
                .total(page.getTotalElements()).current(page.getNumber() + 1).build();
        OperationController.PageableData<PlayerOnlineGiftInfo> data = new OperationController.PageableData<>();
        data.setList(page.getContent());
        data.setPagination(pagination);
        return data;
    }

    @RequestMapping(value = "/PlayerOnlineGift/{id}", method = RequestMethod.DELETE)
    public Object delete(@PathVariable("id") String id) {
        PlayerOnlineGiftInfo playerOnlineGiftInfo = playerOnlineGiftInfoDao.findById(id).get();
        if (playerOnlineGiftInfo.isDeleted())
            return true;
        List<Server> serverList = serverDao.findAllByWgsName(playerOnlineGiftInfo.getDetail().getWgs());
        List<Map<String, Object>> result = new ArrayList<>();
        for (Server server : serverList) {
            if (!playerOnlineGiftInfo.getSeverIds().contains(server.getServerId()))
                continue;
            String cmdAndParam = " rolebatch onlinegiftscancel "
                    + playerOnlineGiftInfo.getId();

            Creator creator = WgsClient.getInstance().getCreatorByType(playerOnlineGiftInfo.getDetail().getWgs());
            String cmdline = "gsd executeGM " + server.getServerId() + cmdAndParam;
            long identifier = Rpc.nextIdentifier();
            GmCmdRequest gmCmdRequest = new GmCmdRequest();
            gmCmdRequest.identifier = identifier;
            gmCmdRequest.cmdline = cmdline;
            gmCmdRequest.gmaccount = "admin";
            Object r;
            try {
                gnet.GmCmdResponse rpcResult = new Rpc(identifier, gmCmdRequest)
                        .sendAndWaitResult(WgsClient.getInstance().get(creator), ********);
                r = rpcResult != null ? rpcResult.result : "执行超时";
            } catch (Rpc.SendFailException e) {
                r = "服务器连接失败";
            }
            Map<String, Object> m = new HashMap<>();
            m.put("serverId", server.getServerId());
            m.put("result", r);
            result.add(m);
        }
        playerOnlineGiftInfo.setDeleted(true);
        playerOnlineGiftInfoDao.save(playerOnlineGiftInfo);
        return true;
    }

    @RequestMapping(value = "/PlayerOnlineGift/{id}/csv", method = RequestMethod.GET)
    public ResponseEntity<Resource> getCSV(@PathVariable("id") String id) {
        PlayerOnlineGiftInfo playerOnlineGiftInfo = playerOnlineGiftInfoDao.findById(id).get();
        ByteArrayResource resource = new ByteArrayResource(playerOnlineGiftInfo.getCsv());
        HttpHeaders headers = new HttpHeaders();

        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(resource.contentLength())
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(resource);

    }
}
