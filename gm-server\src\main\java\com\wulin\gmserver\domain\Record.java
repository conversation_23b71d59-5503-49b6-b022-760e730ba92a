package com.wulin.gmserver.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.Date;
import java.util.List;

@Data
public class Record {
    @Id
    private String id;
    @Indexed
    @DBRef
    private User owner;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date time;

    private List<ServerOperationRecord> msg;

    private String command;


    @Data
    public static class ServerOperationRecord{
        private Integer serverId;
        private String result;
    }
}
