package com.wulin.gmserver.service;

import com.wulin.gmserver.dao.*;
import com.wulin.gmserver.domain.*;
import com.wulin.gmserver.domain.gm.Server;
import com.wulin.gmserver.domain.paramfilter.AbstractParamFilter;
import com.wulin.gmserver.xio.Rpc;
import com.wulin.gmserver.xio.WgsClient;
import gnet.GmCmdRequest;
import gnet.GmCmdResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import xio.Creator;
import xio.Protocol;

import java.util.*;

@Service
public class CommandService {

    @Autowired
    public CommandDao commandDao;

    @Autowired
    PermissionDao permissionDao;

    @Autowired
    MenuDao menuDao;

    public String doCommand(Collection<Server> servers, Command command, Collection<String> params) {
        return "doCommand";
    }

    @Autowired
    RecordDao recordDao;

    @Autowired
    GMModuleDao gmModuleDao;

    @Autowired
    ServerDao serverDao;

    @Autowired
    UserDao userDao;

    public List<Map<String, Object>> callCommandBySchedule(List<String> serverIds, String commandName,
            Collection<String> params, String wgs,
            Long roleId, String userName) {
        Iterable<Server> servers;

        if (serverIds == null || serverIds.isEmpty()) {
            servers = serverDao.findAllByWgsName("WgsdConnector");
        } else {
            servers = serverDao.findAllById(serverIds);
        }

        User user = userDao.findByUserName(userName).get();
        Command command = commandDao.findByName(commandName).orElse(null);

        return callCommand(servers, command, params, wgs, roleId, user);
    }

    public List<Map<String, Object>> callCommand(Iterable<Server> servers, Command command,
            Collection<String> params, String wgs,
            Long roleId, User user) {

        List<Map<String, Object>> result = new ArrayList<>();

        String paramStr = params.stream().map(a -> {
            if (a.indexOf('"') == -1)
                return a;
            StringBuilder s = new StringBuilder(a);
            int index = a.length();
            while ((index = a.lastIndexOf('"', index - 1)) > -1)
                s.insert(index, '\'');
            return s.toString();
        }).map(a -> " \"" + a + "\"").reduce((a, b) -> a + b).orElse("");

        String cmdAndParam = " " + command.getName().replace('_', ' ') + paramStr;
        Creator creator = WgsClient.getInstance().getCreatorByType(wgs);

        for (Server server : servers) {
            Object r;
            String cmdline = "gsd executeGM " + server.getServerId() + cmdAndParam;
            long identifier = Rpc.nextIdentifier();

            GmCmdRequest gmCmdRequest = new GmCmdRequest();
            gmCmdRequest.identifier = identifier;
            gmCmdRequest.cmdline = cmdline;
            gmCmdRequest.gmaccount = "admin";

            if (command.isWithRole()) {
                gmCmdRequest.roleid = roleId;
            }

            try {
                GmCmdResponse rpcResult = (GmCmdResponse) new Rpc(identifier, (Protocol) gmCmdRequest)
                        .sendAndWaitResult(WgsClient.getInstance().get(creator), 100_000L);
                r = (rpcResult != null) ? rpcResult.result : "执行超时";
            } catch (Rpc.SendFailException e) {
                r = "服务器连接失败";
            }

            Map<String, Object> m = new HashMap<>();
            m.put("serverId", server.getServerId());
            m.put("result", r);
            result.add(m);
        }

        Record record = new Record();
        record.setOwner(user);
        record.setTime(new Date());
        record.setCommand(cmdAndParam);

        List<Record.ServerOperationRecord> msg = new ArrayList<>();
        result.forEach(r -> {
            Record.ServerOperationRecord sr = new Record.ServerOperationRecord();
            sr.setResult((String) r.get("result"));
            sr.setServerId((Integer) r.get("serverId"));
            msg.add(sr);
        });

        record.setMsg(msg);
        recordDao.save(record);

        return result;
    }

    public boolean checkAuth(Command command, User user, List<String> params) {
        String commandName = command.getName().replace('.', '_');
        SysRole.ParamWithFilters paramFilterMap = (SysRole.ParamWithFilters) user.getRole().getCommandFilter()
                .get(commandName);

        if (paramFilterMap != null) {
            for (int i = 0; i < command.getParams().size(); i++) {
                Param param = command.getParams().get(i);
                String paramName = param.getName().replace('.', '_');

                if (paramFilterMap.getParamFilters().containsKey(paramName)) {
                    String p = params.get(i);
                    SysRole.AbstractParamFilterList filters = (SysRole.AbstractParamFilterList) paramFilterMap
                            .getParamFilters().get(paramName);

                    for (AbstractParamFilter filter : filters.getFilters()) {
                        if (!filter.filter(p)) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }
}