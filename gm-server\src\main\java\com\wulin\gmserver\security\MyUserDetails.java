package com.wulin.gmserver.security;

import com.wulin.gmserver.domain.User;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class MyUserDetails 
extends org.springframework.security.core.userdetails.User {
    private final User user;
    public MyUserDetails(User user) {
        super(user.getUserName(), (user.getPassword() != null) ? user.getPassword() : "123456", makeGA(user));
        this.user = user;
    }

    private static Collection<SimpleGrantedAuthority> makeGA(User user){
        List<SimpleGrantedAuthority> list = user.getRole().getPermissions().stream()
                .map(permission->new SimpleGrantedAuthority(permission.getPermission())).collect(Collectors.toList());
        list.add(new SimpleGrantedAuthority("ROLE_" + user.getRole().getRoleName()));
        return list;
    }

    public User getUser() {
        return user;
    }
}
