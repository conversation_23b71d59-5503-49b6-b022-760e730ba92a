package com.wulin.gmserver.controller;

import com.wulin.gmserver.dao.PermissionDao;
import com.wulin.gmserver.dao.SysRoleDao;
import com.wulin.gmserver.domain.SysPermission;
import com.wulin.gmserver.domain.SysRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
public class PermissionController {
    @Autowired
    SysRoleDao roleDao;

    @Autowired
    PermissionDao permissionDao;

    @PreAuthorize("hasRole('Admin')")
    @RequestMapping(value = "/roles/{roleId}/permissions", method = RequestMethod.POST)
    public Object delete(@PathVariable String roleId, @RequestBody Map<String, String> map) {
        String permissionId = map.get("permissionId");
        boolean checked = "true".equals(map.get("checked"));
        SysRole role = roleDao.findById(roleId).get();
        SysPermission permission = permissionDao.findById(permissionId).get();
        if (checked) {
            role.getPermissions().add(permission);
        } else {
            role.getPermissions().remove(permission);
        }
        roleDao.save(role);
        return true;
    }
}
