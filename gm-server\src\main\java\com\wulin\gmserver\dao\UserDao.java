package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.SysRole;
import com.wulin.gmserver.domain.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.data.rest.core.annotation.RestResource;
import org.springframework.data.rest.core.config.Projection;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Date;
import java.util.Optional;

@Projection(name ="inlineAddress", types = { User.class })
interface UserInlineAddressProjection {
    String getUserName();
    SysRole getRole();
    Date getLastLoginTime();
}
@RepositoryRestResource(excerptProjection = UserInlineAddressProjection.class)
public interface UserDao extends MongoRepository<User, String> {
    @PreAuthorize("hasRole('Admin')")
    @RestResource
    @Override
    void deleteById(String s);

    @PreAuthorize("hasRole('Admin')")
    @RestResource
    @Override
    void delete(User entity);

    @PreAuthorize("hasRole('Admin')")
    @RestResource
    @Override
    Page<User> findAll(Pageable pageable);

    Optional<User> findByUserName(String userName);
}
