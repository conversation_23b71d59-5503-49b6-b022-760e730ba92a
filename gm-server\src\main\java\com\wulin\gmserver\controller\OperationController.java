package com.wulin.gmserver.controller;

import com.wulin.gmserver.dao.RecordDao;
import com.wulin.gmserver.domain.Record;
import lombok.Builder;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class OperationController {
    @Autowired
    RecordDao recordDao;

    @Data
    @Builder
    public static class Pagination {
        private long total;
        private int current;
        private int pageSize;
    }

    @Data
    public static class PageableData<T> {
        private Pagination pagination;
        private List<T> list;
    }

    @RequestMapping(value = "/records", method = RequestMethod.GET)
    public Object getAll(@RequestParam(defaultValue = "1") int currentPage, @RequestParam(defaultValue = "10") int pageSize){
        PageRequest pageRequest = PageRequest.of(currentPage - 1, pageSize);
        Page<Record> page =  recordDao.findAll(pageRequest);
        Pagination pagination = Pagination.builder().pageSize(page.getSize())
                .total(page.getTotalElements()).current(page.getNumber() + 1).build();
        PageableData<Record> data = new PageableData<>();
        data.setList(page.getContent());
        data.setPagination(pagination);
        return data;
    }
}
