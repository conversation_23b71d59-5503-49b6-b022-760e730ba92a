module.exports = {
  presets: [
    ["@babel/preset-env", {
      "targets": {
        "browsers": ["> 1%", "last 2 versions", "not ie <= 10"]
      },
      "useBuiltIns": "entry",
      "corejs": 2
    }]
  ],
  plugins: [
    // 1. decorators 必须最先
    ["@babel/plugin-proposal-decorators", { legacy: true }],

    // 2. class-properties 紧随其后
    ["@babel/plugin-proposal-class-properties", { loose: true }],

    // 3. pipeline‑operator 放第三
    ["@babel/plugin-proposal-pipeline-operator", { proposal: "minimal" }],

    // 4. transform‑runtime：把 polyfill/Helper 指向 @babel/runtime‑corejs2 + core-js@2
    [
      "@babel/plugin-transform-runtime",
      {
        corejs: 2,
        helpers: true,
        regenerator: true
      }
    ]
  ]
};
