<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.wulin.gmserver.GmServerApplicationTests" tests="1" skipped="0" failures="0" errors="0" timestamp="2025-06-09T07:57:24" hostname="PWRD-20230802BV" time="0.114">
  <properties/>
  <testcase name="contextLoads" classname="com.wulin.gmserver.GmServerApplicationTests" time="0.114"/>
  <system-out><![CDATA[15:57:19.246 [Test worker] DEBUG org.springframework.test.context.junit4.SpringJUnit4ClassRunner - SpringJUnit4ClassRunner constructor called with [class com.wulin.gmserver.GmServerApplicationTests]
15:57:19.252 [Test worker] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating CacheAwareContextLoaderDelegate from class [org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate]
15:57:19.261 [Test worker] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating BootstrapContext using constructor [public org.springframework.test.context.support.DefaultBootstrapContext(java.lang.Class,org.springframework.test.context.CacheAwareContextLoaderDelegate)]
15:57:19.277 [Test worker] DEBUG org.springframework.test.context.BootstrapUtils - Instantiating TestContextBootstrapper for test class [com.wulin.gmserver.GmServerApplicationTests] from class [org.springframework.boot.test.context.SpringBootTestContextBootstrapper]
15:57:19.291 [Test worker] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.wulin.gmserver.GmServerApplicationTests], using SpringBootContextLoader
15:57:19.294 [Test worker] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.wulin.gmserver.GmServerApplicationTests]: class path resource [com/wulin/gmserver/GmServerApplicationTests-context.xml] does not exist
15:57:19.294 [Test worker] DEBUG org.springframework.test.context.support.AbstractContextLoader - Did not detect default resource location for test class [com.wulin.gmserver.GmServerApplicationTests]: class path resource [com/wulin/gmserver/GmServerApplicationTestsContext.groovy] does not exist
15:57:19.295 [Test worker] INFO org.springframework.test.context.support.AbstractContextLoader - Could not detect default resource locations for test class [com.wulin.gmserver.GmServerApplicationTests]: no resource found for suffixes {-context.xml, Context.groovy}.
15:57:19.296 [Test worker] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.wulin.gmserver.GmServerApplicationTests]: GmServerApplicationTests does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
15:57:19.339 [Test worker] DEBUG org.springframework.test.context.support.ActiveProfilesUtils - Could not find an 'annotation declaring class' for annotation type [org.springframework.test.context.ActiveProfiles] and class [com.wulin.gmserver.GmServerApplicationTests]
15:57:19.345 [Test worker] DEBUG org.springframework.core.env.StandardEnvironment - Adding PropertySource 'systemProperties' with lowest search precedence
15:57:19.345 [Test worker] DEBUG org.springframework.core.env.StandardEnvironment - Adding PropertySource 'systemEnvironment' with lowest search precedence
15:57:19.346 [Test worker] DEBUG org.springframework.core.env.StandardEnvironment - Initialized StandardEnvironment with PropertySources [MapPropertySource@2068145220 {name='systemProperties', properties={java.runtime.name=Java(TM) SE Runtime Environment, sun.boot.library.path=C:\Program Files\Java\jdk1.8.0_241\jre\bin, java.vm.version=25.241-b07, java.vm.vendor=Oracle Corporation, java.vendor.url=http://java.oracle.com/, path.separator=;, java.vm.name=Java HotSpot(TM) 64-Bit Server VM, file.encoding.pkg=sun.io, user.script=, sun.java.launcher=SUN_STANDARD, user.country=CN, sun.os.patch.level=, java.vm.specification.name=Java Virtual Machine Specification, user.dir=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server, java.runtime.version=1.8.0_241-b07, java.awt.graphicsenv=sun.awt.Win32GraphicsEnvironment, java.endorsed.dirs=C:\Program Files\Java\jdk1.8.0_241\jre\lib\endorsed, os.arch=amd64, java.io.tmpdir=C:\Users\<USER>\AppData\Local\Temp\, line.separator=
, java.vm.specification.vendor=Oracle Corporation, user.variant=, os.name=Windows 10, sun.jnu.encoding=GBK, java.library.path=C:\Program Files\Java\jdk1.8.0_241\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;F:\GPUToolkit\v12.1\bin;F:\GPUToolkit\v12.1\libnvvp;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Program Files\PlasticSCM5\server;C:\Program Files\PlasticSCM5\client;C:\Python27\;C:\Python27\Scripts;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files (x86)\PDFtk\bin\;C:\ProgramData\chocolatey\bin;C:\Program Files\wkhtmltopdf\bin;C:\Program Files\Java\jdk-21\bin;C:\Program Files\Java\jdk-21\jre\bin;C:\Python26;C:\Program Files\dotnet\;C:\Program Files (x86)\NASM;C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin;C:\Program Files\CMake\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;F:\gradle-7.4-bin\gradle-7.4\bin;F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platform-tools;F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\tools;C:\Program Files\Git\cmd;C:\Program Files\NVIDIA Corporation\Nsight Compute 2023.1.1\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;F:\miniconda3;F:\miniconda3\Library\bin;F:\miniconda3\Scripts;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\chocolatey\lib\maven\apache-maven-3.9.9\bin;C:\Program Files\TortoiseSVN\bin;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;F:\Program Files\010 Editor;%NVM_HOME%;%NVM_SYMLINK%;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.dotnet\tools;E:\nvm;C:\Program Files\nodejs;E:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;., java.specification.name=Java Platform API Specification, java.class.version=52.0, org.gradle.native=false, sun.management.compiler=HotSpot 64-Bit Tiered Compilers, os.version=10.0, user.home=C:\Users\<USER>\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\test;G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\resources\test;G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\main;G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\resources\main;G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\lib\xdb.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-quartz\2.0.2.BUILD-SNAPSHOT\cec35ae1c2b21fe1b8d0cdd33f17a21be048f477\spring-boot-starter-quartz-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-validation\2.0.2.BUILD-SNAPSHOT\45c65a9e5205303b99fe78f37605b9c04275c7a5\spring-boot-starter-validation-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-data-rest\2.0.2.BUILD-SNAPSHOT\3822052029c1ee0254f9d80ee70eca4f232a4bdf\spring-boot-starter-data-rest-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-web\2.0.2.BUILD-SNAPSHOT\df3bfa6716351d81e01a8b23776a96fd9714d8cd\spring-boot-starter-web-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-data-mongodb\2.0.2.BUILD-SNAPSHOT\272571f3963986f5d75a6a10a06124afa976bd08\spring-boot-starter-data-mongodb-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-security\2.0.2.BUILD-SNAPSHOT\77f96e3236c2e449998ced99f26fa12d4c42d156\spring-boot-starter-security-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-rest-hal-browser\3.0.7.RELEASE\2cb161a39a31a22c3773a05a851a70ae980df9b8\spring-data-rest-hal-browser-3.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-csv\1.8\37ca9a9aa2d4be2599e55506a6d3170dd7a3df4\commons-csv-1.8.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-test\2.0.2.BUILD-SNAPSHOT\748c47215b42e5a8013ab7c3503a664d26ef0193\spring-boot-starter-test-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-json\2.0.2.BUILD-SNAPSHOT\a8faf419777cf9c87c117aa18f39d77467fc2856\spring-boot-starter-json-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter\2.0.2.BUILD-SNAPSHOT\d99e7a8bfac681b2bc5d829ff43267e4d1fe957c\spring-boot-starter-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-context-support\5.0.6.RELEASE\9961449bf84597c056fc82ed2f7e4148e15a8397\spring-context-support-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-mongodb\2.0.7.RELEASE\e5c5d40d4dce7ce909e1dce87baddeeba934e1f0\spring-data-mongodb-2.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-rest-webmvc\3.0.7.RELEASE\684949215006f844591c6a8632356f483752fca0\spring-data-rest-webmvc-3.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-rest-core\3.0.7.RELEASE\dd56b4f427987e1d5ab320eee6c3401e32557bb2\spring-data-rest-core-3.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-tx\5.0.6.RELEASE\1b6ffc44f2d59507a0fce1084fcc21788b13904\spring-tx-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.quartz-scheduler\quartz\2.3.0\a090397102a12f6241177c5d501835334bb7662a\quartz-2.3.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-tomcat\2.0.2.BUILD-SNAPSHOT\5ad7f5265b887d758cee6433c6eeb9a524076b11\spring-boot-starter-tomcat-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.tomcat.embed\tomcat-embed-el\8.5.31\3a536e1ac71b82627c3a7408eb38fa0704cb9034\tomcat-embed-el-8.5.31.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hibernate.validator\hibernate-validator\6.0.9.Final\b149e4cce82379f11f6129eb3187ca8ae5404005\hibernate-validator-6.0.9.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.hateoas\spring-hateoas\0.24.0.RELEASE\b6401a781a58e0b2ab95242a553683e04300fe69\spring-hateoas-0.24.0.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-webmvc\5.0.6.RELEASE\1361c5d1cf46665ee01de5dc1ca447c3be174c19\spring-webmvc-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.security\spring-security-web\5.0.5.RELEASE\14b987fee57348c373c5228e23ceb244d872e298\spring-security-web-5.0.5.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-web\5.0.6.RELEASE\89e89de3025165998118590613aedc6df8d64af6\spring-web-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mongodb\mongodb-driver\3.6.3\d462fcc4640ac69b35e7cd2491e992c6bdf82862\mongodb-driver-3.6.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.security\spring-security-config\5.0.5.RELEASE\5d360adc638fc5f55e7364f2544a962ae26e0cfd\spring-security-config-5.0.5.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-test-autoconfigure\2.0.2.BUILD-SNAPSHOT\5532b70f1278375703516bb5ee394e3d1585562b\spring-boot-test-autoconfigure-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-test\2.0.2.BUILD-SNAPSHOT\a88fc625e6203f2c3afa4ad74826fbcc5493ca42\spring-boot-test-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-autoconfigure\2.0.2.BUILD-SNAPSHOT\250e3b1e7a185d79c1c4bbeb266109e8581fef02\spring-boot-autoconfigure-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot\2.0.2.BUILD-SNAPSHOT\62da88d2a3802caf71fdccaef93a935544676ee9\spring-boot-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.security\spring-security-core\5.0.5.RELEASE\d8fb6f4bcb03a1b124b4a0a50b574978496ae0ba\spring-security-core-5.0.5.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.plugin\spring-plugin-core\1.2.0.RELEASE\f380e7760032e7d929184f8ad8a33716b75c0657\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-context\5.0.6.RELEASE\8b73b742ef0ddce04f0f1068f305de240b00ce0d\spring-context-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-aop\5.0.6.RELEASE\74078df698992054c1c1a8f705763a6c7b4b914c\spring-aop-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.jayway.jsonpath\json-path\2.4.0\765a4401ceb2dc8d40553c2075eb80a8fa35c2ae\json-path-2.4.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-logging\2.0.2.BUILD-SNAPSHOT\723390bac9010535484c4856e58ee32e8686693a\spring-boot-starter-logging-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\ch.qos.logback\logback-classic\1.2.3\7c4f3c474fb2c041d8028740440937705ebb473a\logback-classic-1.2.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-to-slf4j\2.10.0\f7e631ccf49cfc0aefa4a2a728da7d374c05bd3c\log4j-to-slf4j-2.10.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\jul-to-slf4j\1.7.25\af5364cd6679bfffb114f0dec8a157aaa283b76\jul-to-slf4j-1.7.25.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-commons\2.0.7.RELEASE\7faa0fb2ffa50a5c50ad9f10118095ae24d5c6e1\spring-data-commons-2.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\1.7.25\da76ca59f6a57ee3102f8f9bd9cee742973efa8a\slf4j-api-1.7.25.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.12\2973d150c0dc1fefe998f834810d68f278ea58ec\junit-4.12.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.assertj\assertj-core\3.9.1\c5ce126b15f28d56cd8f960c1a6a058b9c9aea87\assertj-core-3.9.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mockito\mockito-core\2.15.0\b84bfbbc29cd22c9529409627af6ea2897f4fa85\mockito-core-2.15.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.skyscreamer\jsonassert\1.5.0\6c9d5fe2f59da598d9aefc1cfc6528ff3cf32df3\jsonassert-1.5.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-test\5.0.6.RELEASE\dbc99b288774b2e07d94bc14fe38d556bc3ed34a\spring-test-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-expression\5.0.6.RELEASE\dec57ae5e6f0dfd4c6cfc199aa13ed268c3e1a65\spring-expression-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-beans\5.0.6.RELEASE\d609b83cd8a71650a70778cf8d02c9a05b9161fe\spring-beans-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-core\5.0.6.RELEASE\984a52455b1be596b7f1a29f62d21d2f483eb764\spring-core-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.xmlunit\xmlunit-core\2.5.1\4ffdb346572a7356f7521cd3119ce5287d2e339d\xmlunit-core-2.5.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.annotation\javax.annotation-api\1.3.2\934c04d3cfef185a8008e7bf34331b79730a9d43\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.yaml\snakeyaml\1.19\2d998d3d674b172a588e54ab619854d073f555b5\snakeyaml-1.19.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mchange\mchange-commons-java\0.2.11\2a6a6c1fe25f28f5a073171956ce6250813467ef\mchange-commons-java-0.2.11.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.validation\validation-api\2.0.1.Final\cb855558e6271b1b32e716d24cb85c7f583ce09e\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jboss.logging\jboss-logging\3.3.2.Final\3789d00e859632e6c6206adc0c71625559e6e3b0\jboss-logging-3.3.2.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml\classmate\1.3.4\3d5f48f10bbe4eb7bd862f10c0583be2e0053c6\classmate-1.3.4.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.datatype\jackson-datatype-jdk8\2.9.5\23e37f085279ba316c0df923513b81609e1d1f6\jackson-datatype-jdk8-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.datatype\jackson-datatype-jsr310\2.9.5\d1f0d11e816bc04e222a261106ca138801841c2d\jackson-datatype-jsr310-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.module\jackson-module-parameter-names\2.9.5\f824c60751ffb7bfc3a0d30dfe0e42317d8e67f5\jackson-module-parameter-names-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-databind\2.9.5\3490508379d065fe3fcb80042b62f630f7588606\jackson-databind-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.tomcat.embed\tomcat-embed-websocket\8.5.31\6564e716b89de5eaa0dd234ae7989576503ddf3\tomcat-embed-websocket-8.5.31.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.tomcat.embed\tomcat-embed-core\8.5.31\f5adf7ed8c34aa005b22b6a2dc7b6796e10e9c79\tomcat-embed-core-8.5.31.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mongodb\mongodb-driver-core\3.6.3\f2c41ad5349cdb65a6f7bde16f5ebae9a0dbe5f5\mongodb-driver-core-3.6.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mongodb\bson\3.6.3\6c85ddf1fc96eb8776213bef6665d005a564ecd3\bson-3.6.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-annotations\2.9.0\7c10d545325e3a6e72e06381afe469fd40eb701\jackson-annotations-2.9.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minidev\json-smart\2.3\7396407491352ce4fa30de92efb158adb76b5b\json-smart-2.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.bytebuddy\byte-buddy\1.7.11\f02857a4e2c66ccbe7aaad6100a0a6c461bce9b3\byte-buddy-1.7.11.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.bytebuddy\byte-buddy-agent\1.7.11\b425a8933ca07edd03c6dbc8bc3b595fba9780de\byte-buddy-agent-1.7.11.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.objenesis\objenesis\2.6\639033469776fd37c08358c6b92a4761feb2af4b\objenesis-2.6.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.vaadin.external.google\android-json\0.0.20131108.vaadin1\fa26d351fe62a6a17f5cda1287c1c6110dec413f\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-jcl\5.0.6.RELEASE\295ee4e8657e9c1fd327735c01d5fbce339cc44d\spring-jcl-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-core\2.9.5\a22ac51016944b06fd9ffbc9541c6e7ce5eea117\jackson-core-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.atteo\evo-inflector\1.2.2\2551aad98d65ac5464d81fe05f0e1516cfe471c9\evo-inflector-1.2.2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minidev\accessors-smart\1.2\c592b500269bfde36096641b01238a8350f8aa31\accessors-smart-1.2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\ch.qos.logback\logback-core\1.2.3\864344400c3d4d92dfeb0a305dc87d953677c03c\logback-core-1.2.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-api\2.10.0\fec5797a55b786184a537abd39c3fa1449d752d6\log4j-api-2.10.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm\5.0.4\da08b8cce7bbf903602a25a3a163ae252435795\asm-5.0.4.jar, user.name=Administrator, java.vm.specification.version=1.8, sun.java.command=worker.org.gradle.process.internal.worker.GradleWorkerMain 'Gradle Test Executor 1', java.home=C:\Program Files\Java\jdk1.8.0_241\jre, sun.arch.data.model=64, user.language=zh, java.specification.vendor=Oracle Corporation, awt.toolkit=sun.awt.windows.WToolkit, java.vm.info=mixed mode, java.version=1.8.0_241, java.ext.dirs=C:\Program Files\Java\jdk1.8.0_241\jre\lib\ext;C:\Windows\Sun\Java\lib\ext, sun.boot.class.path=C:\Program Files\Java\jdk1.8.0_241\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\rt.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\sunrsasign.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_241\jre\classes, java.vendor=Oracle Corporation, org.gradle.test.worker=1, file.separator=\, java.vendor.url.bug=http://bugreport.sun.com/bugreport/, sun.io.unicode.encoding=UnicodeLittle, sun.cpu.endian=little, sun.desktop=windows, sun.cpu.isalist=amd64}}, SystemEnvironmentPropertySource@369888566 {name='systemEnvironment', properties={NVM_SYMLINK=C:\Program Files\nodejs, SESSIONNAME=Console, ALLUSERSPROFILE=C:\ProgramData, GATEWAY_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\gateway.vmoptions, ANDROID_HOME=F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK, RUSTROVER_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\rustrover.vmoptions, SystemDrive=C:, VSCODE_INJECTION=1, COLORTERM=truecolor, DIRNAME=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\, USERNAME=Administrator, VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe, JDK-21=C:\Program Files\Java\jdk-21, STUDIO_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\studio.vmoptions, TERM_PROGRAM_VERSION=1.100.3, CMD_LINE_ARGS=clean build, ProgramFiles(x86)=C:\Program Files (x86), FPS_BROWSER_USER_PROFILE_STRING=Default, PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL, APPCODE_VM_OPTIONS=D:\Downloads\jetbra-1126574a2f82debceb72e7f948eb7d4f616ffddf\jetbra\vmoptions\appcode.vmoptions, DriverData=C:\Windows\System32\Drivers\DriverData, IntelliJ IDEA Community Edition=E:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\bin;, HOMEPATH=\Users\Administrator, ESET_OPTIONS=                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               , PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 151 Stepping 2, GenuineIntel, ProgramFiles=C:\Program Files, PUBLIC=C:\Users\<USER>\Windows, ZES_ENABLE_SYSMAN=1, _SKIP=2, FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer, WEBSTORM_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\webstorm.vmoptions, JAVA_HOME=C:\Program Files\Java\jdk1.8.0_241, PROMPT=$P$G, CLION_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\clion.vmoptions, GOLAND_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\goland.vmoptions, OneDrive=C:\Users\<USER>\OneDrive, APPDATA=C:\Users\<USER>\AppData\Roaming, JAVA_EXE=C:\Program Files\Java\jdk1.8.0_241/bin/java.exe, ChocolateyInstall=C:\ProgramData\chocolatey, JETBRAINS_CLIENT_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\jetbrains_client.vmoptions, SVN_EXPERIMENTAL_COMMANDS=shelf2, Path=F:\GPUToolkit\v12.1\bin;F:\GPUToolkit\v12.1\libnvvp;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Program Files\PlasticSCM5\server;C:\Program Files\PlasticSCM5\client;C:\Python27\;C:\Python27\Scripts;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files (x86)\PDFtk\bin\;C:\ProgramData\chocolatey\bin;C:\Program Files\wkhtmltopdf\bin;C:\Program Files\Java\jdk-21\bin;C:\Program Files\Java\jdk-21\jre\bin;C:\Python26;C:\Program Files\dotnet\;C:\Program Files (x86)\NASM;C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin;C:\Program Files\CMake\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;F:\gradle-7.4-bin\gradle-7.4\bin;F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platform-tools;F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\tools;C:\Program Files\Git\cmd;C:\Program Files\NVIDIA Corporation\Nsight Compute 2023.1.1\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;F:\miniconda3;F:\miniconda3\Library\bin;F:\miniconda3\Scripts;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\chocolatey\lib\maven\apache-maven-3.9.9\bin;C:\Program Files\TortoiseSVN\bin;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;F:\Program Files\010 Editor;%NVM_HOME%;%NVM_SYMLINK%;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.dotnet\tools;E:\nvm;C:\Program Files\nodejs;E:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand, VCINSTALLDIR=C:\Program Files\Microsoft Visual Studio\2022\Community\VC, OLLAMA_MODELS=F:\AiProject\models, TERM_PROGRAM=vscode, COMPUTERNAME=PWRD-20230802BV, ComSpec=C:\Windows\system32\cmd.exe, APP_BASE_NAME=gradlew, WEBIDE_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\webide.vmoptions, HOMEDRIVE=C:, NANT_HOME=C:\Program Files\nant-0.92\bin, NUMBER_OF_PROCESSORS=20, USERDOMAIN_ROAMINGPROFILE=PWRD-20230802BV, PROCESSOR_LEVEL=6, ADSK_3DSMAX_x64_2021=E:\Program Files\AutoDesk\3ds Max 2021\, PROCESSOR_ARCHITECTURE=AMD64, JDK-8=C:\Program Files\Java\jdk1.8.0_241, VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-96623f9bce-sock, PSModulePath=D:\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules;E:\Program Files\VisualSVN Server\PowerShellModules, RIDER_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\rider.vmoptions, DEVECOSTUDIO_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\devecostudio.vmoptions, GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh, VISUALSVN_SERVER=E:\Program Files\VisualSVN Server\, CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_20100_HVDNLTAAZVITSIXE, CUDA_PATH_V12_1=F:\GPUToolkit\v12.1, APP_HOME=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\, DATASPELL_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\dataspell.vmoptions, ORIGINAL_XDG_CURRENT_DESKTOP=undefined, ProgramData=C:\ProgramData, ProgramW6432=C:\Program Files, BSPRINT_CLIENT=C:/Users/<USER>/AppData/Roaming/Brocadesoft, DATAGRIP_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\datagrip.vmoptions, LOCALAPPDATA=C:\Users\<USER>\AppData\Local, ChocolateyLastPathUpdate=133849427830057550, USERDOMAIN=PWRD-20230802BV, LOGONSERVER=\\PWRD-20230802BV, PYCHARM_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\pycharm.vmoptions, WIX=C:\Program Files (x86)\WiX Toolset v3.14\, JETBRAINSCLIENT_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\jetbrainsclient.vmoptions, LANG=en_US.UTF-8, =G:=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server, VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js, IDEA_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\idea.vmoptions, AQUA_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\aqua.vmoptions, RUBYMINE_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\rubymine.vmoptions, VSCODE_GIT_ASKPASS_EXTRA_ARGS=, CommonProgramFiles=C:\Program Files\Common Files, OS=Windows_NT, CUDA_PATH=F:\GPUToolkit\v12.1, NVM_HOME=E:\nvm, PROCESSOR_REVISION=9702, CLASSPATH=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\\gradle\wrapper\gradle-wrapper.jar, CommonProgramW6432=C:\Program Files\Common Files, SystemRoot=C:\Windows, TEMP=C:\Users\<USER>\AppData\Local\Temp, USERPROFILE=C:\Users\<USER>\Users\Administrator\AppData\Local\Temp, CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files, PHPSTORM_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\phpstorm.vmoptions}}]
15:57:19.356 [Test worker] DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver - Resolved classpath location [com/wulin/gmserver/] to resources [URL [file:/G:/WuLinWork/ProjectWuLin/snail/gm/gm-server/build/classes/java/test/com/wulin/gmserver/], URL [file:/G:/WuLinWork/ProjectWuLin/snail/gm/gm-server/build/classes/java/main/com/wulin/gmserver/]]
15:57:19.358 [Test worker] DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver - Looking for matching resources in directory tree [G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\test\com\wulin\gmserver]
15:57:19.358 [Test worker] DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver - Searching directory [G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\test\com\wulin\gmserver] for files matching pattern [G:/WuLinWork/ProjectWuLin/snail/gm/gm-server/build/classes/java/test/com/wulin/gmserver/*.class]
15:57:19.361 [Test worker] DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver - Looking for matching resources in directory tree [G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\main\com\wulin\gmserver]
15:57:19.361 [Test worker] DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver - Searching directory [G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\main\com\wulin\gmserver] for files matching pattern [G:/WuLinWork/ProjectWuLin/snail/gm/gm-server/build/classes/java/main/com/wulin/gmserver/*.class]
15:57:19.362 [Test worker] DEBUG org.springframework.core.io.support.PathMatchingResourcePatternResolver - Resolved location pattern [classpath*:com/wulin/gmserver/*.class] to resources [file [G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\test\com\wulin\gmserver\GmServerApplicationTests.class], file [G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\main\com\wulin\gmserver\GmServerApplication.class]]
15:57:19.400 [Test worker] DEBUG org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider - Identified candidate component class: file [G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\main\com\wulin\gmserver\GmServerApplication.class]
15:57:19.402 [Test worker] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.wulin.gmserver.GmServerApplication for test class com.wulin.gmserver.GmServerApplicationTests
15:57:19.515 [Test worker] DEBUG org.springframework.boot.test.context.SpringBootTestContextBootstrapper - @TestExecutionListeners is not present for class [com.wulin.gmserver.GmServerApplicationTests]: using defaults.
15:57:19.516 [Test worker] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
15:57:19.535 [Test worker] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@6b42933e, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@2261ed07, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@75143eb1, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@5ccfff00, org.springframework.test.context.support.DirtiesContextTestExecutionListener@23eb4cc4, org.springframework.test.context.transaction.TransactionalTestExecutionListener@e4f846b, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@5a7ad2a5, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@796e0115, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@1aff90, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@72044f24, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@7ee7543b, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@7708945b]
15:57:19.537 [Test worker] DEBUG org.springframework.test.annotation.ProfileValueUtils - Retrieved @ProfileValueSourceConfiguration [null] for test class [com.wulin.gmserver.GmServerApplicationTests]
15:57:19.538 [Test worker] DEBUG org.springframework.test.annotation.ProfileValueUtils - Retrieved ProfileValueSource type [class org.springframework.test.annotation.SystemProfileValueSource] for class [com.wulin.gmserver.GmServerApplicationTests]
15:57:19.539 [Test worker] DEBUG org.springframework.test.annotation.ProfileValueUtils - Retrieved @ProfileValueSourceConfiguration [null] for test class [com.wulin.gmserver.GmServerApplicationTests]
15:57:19.539 [Test worker] DEBUG org.springframework.test.annotation.ProfileValueUtils - Retrieved ProfileValueSource type [class org.springframework.test.annotation.SystemProfileValueSource] for class [com.wulin.gmserver.GmServerApplicationTests]
15:57:19.542 [Test worker] DEBUG org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener - Before test class: context [DefaultTestContext@5caae405 testClass = GmServerApplicationTests, testInstance = [null], testMethod = [null], testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@7c1d956a testClass = GmServerApplicationTests, locations = '{}', classes = '{class com.wulin.gmserver.GmServerApplication}', contextInitializerClasses = '[]', activeProfiles = '{}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@23616e7f, org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@3cd72ea6, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@4ca190ab, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@7fa55779], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true]], class annotated with @DirtiesContext [false] with mode [null].
15:57:19.543 [Test worker] DEBUG org.springframework.test.annotation.ProfileValueUtils - Retrieved @ProfileValueSourceConfiguration [null] for test class [com.wulin.gmserver.GmServerApplicationTests]
15:57:19.544 [Test worker] DEBUG org.springframework.test.annotation.ProfileValueUtils - Retrieved ProfileValueSource type [class org.springframework.test.annotation.SystemProfileValueSource] for class [com.wulin.gmserver.GmServerApplicationTests]
15:57:19.558 [Test worker] DEBUG org.springframework.core.env.StandardEnvironment - Adding PropertySource 'systemProperties' with lowest search precedence
15:57:19.558 [Test worker] DEBUG org.springframework.core.env.StandardEnvironment - Adding PropertySource 'systemEnvironment' with lowest search precedence
15:57:19.559 [Test worker] DEBUG org.springframework.core.env.StandardEnvironment - Initialized StandardEnvironment with PropertySources [MapPropertySource@2062457964 {name='systemProperties', properties={java.runtime.name=Java(TM) SE Runtime Environment, sun.boot.library.path=C:\Program Files\Java\jdk1.8.0_241\jre\bin, java.vm.version=25.241-b07, java.vm.vendor=Oracle Corporation, java.vendor.url=http://java.oracle.com/, path.separator=;, java.vm.name=Java HotSpot(TM) 64-Bit Server VM, file.encoding.pkg=sun.io, user.script=, sun.java.launcher=SUN_STANDARD, user.country=CN, sun.os.patch.level=, java.vm.specification.name=Java Virtual Machine Specification, user.dir=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server, java.runtime.version=1.8.0_241-b07, java.awt.graphicsenv=sun.awt.Win32GraphicsEnvironment, java.endorsed.dirs=C:\Program Files\Java\jdk1.8.0_241\jre\lib\endorsed, os.arch=amd64, java.io.tmpdir=C:\Users\<USER>\AppData\Local\Temp\, line.separator=
, java.vm.specification.vendor=Oracle Corporation, user.variant=, os.name=Windows 10, sun.jnu.encoding=GBK, java.library.path=C:\Program Files\Java\jdk1.8.0_241\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;F:\GPUToolkit\v12.1\bin;F:\GPUToolkit\v12.1\libnvvp;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Program Files\PlasticSCM5\server;C:\Program Files\PlasticSCM5\client;C:\Python27\;C:\Python27\Scripts;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files (x86)\PDFtk\bin\;C:\ProgramData\chocolatey\bin;C:\Program Files\wkhtmltopdf\bin;C:\Program Files\Java\jdk-21\bin;C:\Program Files\Java\jdk-21\jre\bin;C:\Python26;C:\Program Files\dotnet\;C:\Program Files (x86)\NASM;C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin;C:\Program Files\CMake\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;F:\gradle-7.4-bin\gradle-7.4\bin;F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platform-tools;F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\tools;C:\Program Files\Git\cmd;C:\Program Files\NVIDIA Corporation\Nsight Compute 2023.1.1\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;F:\miniconda3;F:\miniconda3\Library\bin;F:\miniconda3\Scripts;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\chocolatey\lib\maven\apache-maven-3.9.9\bin;C:\Program Files\TortoiseSVN\bin;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;F:\Program Files\010 Editor;%NVM_HOME%;%NVM_SYMLINK%;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.dotnet\tools;E:\nvm;C:\Program Files\nodejs;E:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;., java.specification.name=Java Platform API Specification, java.class.version=52.0, org.gradle.native=false, sun.management.compiler=HotSpot 64-Bit Tiered Compilers, os.version=10.0, user.home=C:\Users\<USER>\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\test;G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\resources\test;G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\classes\java\main;G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\build\resources\main;G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\lib\xdb.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-quartz\2.0.2.BUILD-SNAPSHOT\cec35ae1c2b21fe1b8d0cdd33f17a21be048f477\spring-boot-starter-quartz-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-validation\2.0.2.BUILD-SNAPSHOT\45c65a9e5205303b99fe78f37605b9c04275c7a5\spring-boot-starter-validation-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-data-rest\2.0.2.BUILD-SNAPSHOT\3822052029c1ee0254f9d80ee70eca4f232a4bdf\spring-boot-starter-data-rest-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-web\2.0.2.BUILD-SNAPSHOT\df3bfa6716351d81e01a8b23776a96fd9714d8cd\spring-boot-starter-web-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-data-mongodb\2.0.2.BUILD-SNAPSHOT\272571f3963986f5d75a6a10a06124afa976bd08\spring-boot-starter-data-mongodb-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-security\2.0.2.BUILD-SNAPSHOT\77f96e3236c2e449998ced99f26fa12d4c42d156\spring-boot-starter-security-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-rest-hal-browser\3.0.7.RELEASE\2cb161a39a31a22c3773a05a851a70ae980df9b8\spring-data-rest-hal-browser-3.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-csv\1.8\37ca9a9aa2d4be2599e55506a6d3170dd7a3df4\commons-csv-1.8.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-test\2.0.2.BUILD-SNAPSHOT\748c47215b42e5a8013ab7c3503a664d26ef0193\spring-boot-starter-test-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-json\2.0.2.BUILD-SNAPSHOT\a8faf419777cf9c87c117aa18f39d77467fc2856\spring-boot-starter-json-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter\2.0.2.BUILD-SNAPSHOT\d99e7a8bfac681b2bc5d829ff43267e4d1fe957c\spring-boot-starter-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-context-support\5.0.6.RELEASE\9961449bf84597c056fc82ed2f7e4148e15a8397\spring-context-support-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-mongodb\2.0.7.RELEASE\e5c5d40d4dce7ce909e1dce87baddeeba934e1f0\spring-data-mongodb-2.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-rest-webmvc\3.0.7.RELEASE\684949215006f844591c6a8632356f483752fca0\spring-data-rest-webmvc-3.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-rest-core\3.0.7.RELEASE\dd56b4f427987e1d5ab320eee6c3401e32557bb2\spring-data-rest-core-3.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-tx\5.0.6.RELEASE\1b6ffc44f2d59507a0fce1084fcc21788b13904\spring-tx-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.quartz-scheduler\quartz\2.3.0\a090397102a12f6241177c5d501835334bb7662a\quartz-2.3.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-tomcat\2.0.2.BUILD-SNAPSHOT\5ad7f5265b887d758cee6433c6eeb9a524076b11\spring-boot-starter-tomcat-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.tomcat.embed\tomcat-embed-el\8.5.31\3a536e1ac71b82627c3a7408eb38fa0704cb9034\tomcat-embed-el-8.5.31.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hibernate.validator\hibernate-validator\6.0.9.Final\b149e4cce82379f11f6129eb3187ca8ae5404005\hibernate-validator-6.0.9.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.hateoas\spring-hateoas\0.24.0.RELEASE\b6401a781a58e0b2ab95242a553683e04300fe69\spring-hateoas-0.24.0.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-webmvc\5.0.6.RELEASE\1361c5d1cf46665ee01de5dc1ca447c3be174c19\spring-webmvc-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.security\spring-security-web\5.0.5.RELEASE\14b987fee57348c373c5228e23ceb244d872e298\spring-security-web-5.0.5.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-web\5.0.6.RELEASE\89e89de3025165998118590613aedc6df8d64af6\spring-web-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mongodb\mongodb-driver\3.6.3\d462fcc4640ac69b35e7cd2491e992c6bdf82862\mongodb-driver-3.6.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.security\spring-security-config\5.0.5.RELEASE\5d360adc638fc5f55e7364f2544a962ae26e0cfd\spring-security-config-5.0.5.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-test-autoconfigure\2.0.2.BUILD-SNAPSHOT\5532b70f1278375703516bb5ee394e3d1585562b\spring-boot-test-autoconfigure-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-test\2.0.2.BUILD-SNAPSHOT\a88fc625e6203f2c3afa4ad74826fbcc5493ca42\spring-boot-test-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-autoconfigure\2.0.2.BUILD-SNAPSHOT\250e3b1e7a185d79c1c4bbeb266109e8581fef02\spring-boot-autoconfigure-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot\2.0.2.BUILD-SNAPSHOT\62da88d2a3802caf71fdccaef93a935544676ee9\spring-boot-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.security\spring-security-core\5.0.5.RELEASE\d8fb6f4bcb03a1b124b4a0a50b574978496ae0ba\spring-security-core-5.0.5.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.plugin\spring-plugin-core\1.2.0.RELEASE\f380e7760032e7d929184f8ad8a33716b75c0657\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-context\5.0.6.RELEASE\8b73b742ef0ddce04f0f1068f305de240b00ce0d\spring-context-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-aop\5.0.6.RELEASE\74078df698992054c1c1a8f705763a6c7b4b914c\spring-aop-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.jayway.jsonpath\json-path\2.4.0\765a4401ceb2dc8d40553c2075eb80a8fa35c2ae\json-path-2.4.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.boot\spring-boot-starter-logging\2.0.2.BUILD-SNAPSHOT\723390bac9010535484c4856e58ee32e8686693a\spring-boot-starter-logging-2.0.2.BUILD-SNAPSHOT.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\ch.qos.logback\logback-classic\1.2.3\7c4f3c474fb2c041d8028740440937705ebb473a\logback-classic-1.2.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-to-slf4j\2.10.0\f7e631ccf49cfc0aefa4a2a728da7d374c05bd3c\log4j-to-slf4j-2.10.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\jul-to-slf4j\1.7.25\af5364cd6679bfffb114f0dec8a157aaa283b76\jul-to-slf4j-1.7.25.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework.data\spring-data-commons\2.0.7.RELEASE\7faa0fb2ffa50a5c50ad9f10118095ae24d5c6e1\spring-data-commons-2.0.7.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\1.7.25\da76ca59f6a57ee3102f8f9bd9cee742973efa8a\slf4j-api-1.7.25.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.12\2973d150c0dc1fefe998f834810d68f278ea58ec\junit-4.12.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.assertj\assertj-core\3.9.1\c5ce126b15f28d56cd8f960c1a6a058b9c9aea87\assertj-core-3.9.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mockito\mockito-core\2.15.0\b84bfbbc29cd22c9529409627af6ea2897f4fa85\mockito-core-2.15.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.skyscreamer\jsonassert\1.5.0\6c9d5fe2f59da598d9aefc1cfc6528ff3cf32df3\jsonassert-1.5.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-test\5.0.6.RELEASE\dbc99b288774b2e07d94bc14fe38d556bc3ed34a\spring-test-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-expression\5.0.6.RELEASE\dec57ae5e6f0dfd4c6cfc199aa13ed268c3e1a65\spring-expression-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-beans\5.0.6.RELEASE\d609b83cd8a71650a70778cf8d02c9a05b9161fe\spring-beans-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-core\5.0.6.RELEASE\984a52455b1be596b7f1a29f62d21d2f483eb764\spring-core-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.xmlunit\xmlunit-core\2.5.1\4ffdb346572a7356f7521cd3119ce5287d2e339d\xmlunit-core-2.5.1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.annotation\javax.annotation-api\1.3.2\934c04d3cfef185a8008e7bf34331b79730a9d43\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.yaml\snakeyaml\1.19\2d998d3d674b172a588e54ab619854d073f555b5\snakeyaml-1.19.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mchange\mchange-commons-java\0.2.11\2a6a6c1fe25f28f5a073171956ce6250813467ef\mchange-commons-java-0.2.11.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.validation\validation-api\2.0.1.Final\cb855558e6271b1b32e716d24cb85c7f583ce09e\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jboss.logging\jboss-logging\3.3.2.Final\3789d00e859632e6c6206adc0c71625559e6e3b0\jboss-logging-3.3.2.Final.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml\classmate\1.3.4\3d5f48f10bbe4eb7bd862f10c0583be2e0053c6\classmate-1.3.4.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.datatype\jackson-datatype-jdk8\2.9.5\23e37f085279ba316c0df923513b81609e1d1f6\jackson-datatype-jdk8-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.datatype\jackson-datatype-jsr310\2.9.5\d1f0d11e816bc04e222a261106ca138801841c2d\jackson-datatype-jsr310-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.module\jackson-module-parameter-names\2.9.5\f824c60751ffb7bfc3a0d30dfe0e42317d8e67f5\jackson-module-parameter-names-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-databind\2.9.5\3490508379d065fe3fcb80042b62f630f7588606\jackson-databind-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.tomcat.embed\tomcat-embed-websocket\8.5.31\6564e716b89de5eaa0dd234ae7989576503ddf3\tomcat-embed-websocket-8.5.31.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.tomcat.embed\tomcat-embed-core\8.5.31\f5adf7ed8c34aa005b22b6a2dc7b6796e10e9c79\tomcat-embed-core-8.5.31.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mongodb\mongodb-driver-core\3.6.3\f2c41ad5349cdb65a6f7bde16f5ebae9a0dbe5f5\mongodb-driver-core-3.6.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.mongodb\bson\3.6.3\6c85ddf1fc96eb8776213bef6665d005a564ecd3\bson-3.6.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-annotations\2.9.0\7c10d545325e3a6e72e06381afe469fd40eb701\jackson-annotations-2.9.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minidev\json-smart\2.3\7396407491352ce4fa30de92efb158adb76b5b\json-smart-2.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.bytebuddy\byte-buddy\1.7.11\f02857a4e2c66ccbe7aaad6100a0a6c461bce9b3\byte-buddy-1.7.11.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.bytebuddy\byte-buddy-agent\1.7.11\b425a8933ca07edd03c6dbc8bc3b595fba9780de\byte-buddy-agent-1.7.11.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.objenesis\objenesis\2.6\639033469776fd37c08358c6b92a4761feb2af4b\objenesis-2.6.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.vaadin.external.google\android-json\0.0.20131108.vaadin1\fa26d351fe62a6a17f5cda1287c1c6110dec413f\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.springframework\spring-jcl\5.0.6.RELEASE\295ee4e8657e9c1fd327735c01d5fbce339cc44d\spring-jcl-5.0.6.RELEASE.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.fasterxml.jackson.core\jackson-core\2.9.5\a22ac51016944b06fd9ffbc9541c6e7ce5eea117\jackson-core-2.9.5.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.atteo\evo-inflector\1.2.2\2551aad98d65ac5464d81fe05f0e1516cfe471c9\evo-inflector-1.2.2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minidev\accessors-smart\1.2\c592b500269bfde36096641b01238a8350f8aa31\accessors-smart-1.2.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\ch.qos.logback\logback-core\1.2.3\864344400c3d4d92dfeb0a305dc87d953677c03c\logback-core-1.2.3.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-api\2.10.0\fec5797a55b786184a537abd39c3fa1449d752d6\log4j-api-2.10.0.jar;C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm\5.0.4\da08b8cce7bbf903602a25a3a163ae252435795\asm-5.0.4.jar, user.name=Administrator, java.vm.specification.version=1.8, sun.java.command=worker.org.gradle.process.internal.worker.GradleWorkerMain 'Gradle Test Executor 1', java.home=C:\Program Files\Java\jdk1.8.0_241\jre, sun.arch.data.model=64, user.language=zh, java.specification.vendor=Oracle Corporation, awt.toolkit=sun.awt.windows.WToolkit, java.vm.info=mixed mode, java.version=1.8.0_241, java.ext.dirs=C:\Program Files\Java\jdk1.8.0_241\jre\lib\ext;C:\Windows\Sun\Java\lib\ext, sun.boot.class.path=C:\Program Files\Java\jdk1.8.0_241\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\rt.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\sunrsasign.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_241\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_241\jre\classes, java.vendor=Oracle Corporation, org.gradle.test.worker=1, file.separator=\, java.vendor.url.bug=http://bugreport.sun.com/bugreport/, sun.io.unicode.encoding=UnicodeLittle, sun.cpu.endian=little, sun.desktop=windows, sun.cpu.isalist=amd64}}, SystemEnvironmentPropertySource@911433119 {name='systemEnvironment', properties={NVM_SYMLINK=C:\Program Files\nodejs, SESSIONNAME=Console, ALLUSERSPROFILE=C:\ProgramData, GATEWAY_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\gateway.vmoptions, ANDROID_HOME=F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK, RUSTROVER_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\rustrover.vmoptions, SystemDrive=C:, VSCODE_INJECTION=1, COLORTERM=truecolor, DIRNAME=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\, USERNAME=Administrator, VSCODE_GIT_ASKPASS_NODE=C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\Code.exe, JDK-21=C:\Program Files\Java\jdk-21, STUDIO_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\studio.vmoptions, TERM_PROGRAM_VERSION=1.100.3, CMD_LINE_ARGS=clean build, ProgramFiles(x86)=C:\Program Files (x86), FPS_BROWSER_USER_PROFILE_STRING=Default, PATHEXT=.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL, APPCODE_VM_OPTIONS=D:\Downloads\jetbra-1126574a2f82debceb72e7f948eb7d4f616ffddf\jetbra\vmoptions\appcode.vmoptions, DriverData=C:\Windows\System32\Drivers\DriverData, IntelliJ IDEA Community Edition=E:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\bin;, HOMEPATH=\Users\Administrator, ESET_OPTIONS=                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               , PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 151 Stepping 2, GenuineIntel, ProgramFiles=C:\Program Files, PUBLIC=C:\Users\<USER>\Windows, ZES_ENABLE_SYSMAN=1, _SKIP=2, FPS_BROWSER_APP_PROFILE_STRING=Internet Explorer, WEBSTORM_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\webstorm.vmoptions, JAVA_HOME=C:\Program Files\Java\jdk1.8.0_241, PROMPT=$P$G, CLION_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\clion.vmoptions, GOLAND_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\goland.vmoptions, OneDrive=C:\Users\<USER>\OneDrive, APPDATA=C:\Users\<USER>\AppData\Roaming, JAVA_EXE=C:\Program Files\Java\jdk1.8.0_241/bin/java.exe, ChocolateyInstall=C:\ProgramData\chocolatey, JETBRAINS_CLIENT_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\jetbrains_client.vmoptions, SVN_EXPERIMENTAL_COMMANDS=shelf2, Path=F:\GPUToolkit\v12.1\bin;F:\GPUToolkit\v12.1\libnvvp;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Program Files\PlasticSCM5\server;C:\Program Files\PlasticSCM5\client;C:\Python27\;C:\Python27\Scripts;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files (x86)\PDFtk\bin\;C:\ProgramData\chocolatey\bin;C:\Program Files\wkhtmltopdf\bin;C:\Program Files\Java\jdk-21\bin;C:\Program Files\Java\jdk-21\jre\bin;C:\Python26;C:\Program Files\dotnet\;C:\Program Files (x86)\NASM;C:\Strawberry\c\bin;C:\Strawberry\perl\site\bin;C:\Strawberry\perl\bin;C:\Program Files\CMake\bin;C:\Program Files\TortoiseGit\bin;C:\Program Files\Common Files\Autodesk Shared\;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;F:\gradle-7.4-bin\gradle-7.4\bin;F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\platform-tools;F:\Unity\2022.3.22f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\tools;C:\Program Files\Git\cmd;C:\Program Files\NVIDIA Corporation\Nsight Compute 2023.1.1\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;F:\miniconda3;F:\miniconda3\Library\bin;F:\miniconda3\Scripts;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\chocolatey\lib\maven\apache-maven-3.9.9\bin;C:\Program Files\TortoiseSVN\bin;C:\Program Files\Git\cmd;C:\Program Files\Git\usr\bin;F:\Program Files\010 Editor;%NVM_HOME%;%NVM_SYMLINK%;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\.dotnet\tools;E:\nvm;C:\Program Files\nodejs;E:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2025.1.1.1\bin;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand, VCINSTALLDIR=C:\Program Files\Microsoft Visual Studio\2022\Community\VC, OLLAMA_MODELS=F:\AiProject\models, TERM_PROGRAM=vscode, COMPUTERNAME=PWRD-20230802BV, ComSpec=C:\Windows\system32\cmd.exe, APP_BASE_NAME=gradlew, WEBIDE_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\webide.vmoptions, HOMEDRIVE=C:, NANT_HOME=C:\Program Files\nant-0.92\bin, NUMBER_OF_PROCESSORS=20, USERDOMAIN_ROAMINGPROFILE=PWRD-20230802BV, PROCESSOR_LEVEL=6, ADSK_3DSMAX_x64_2021=E:\Program Files\AutoDesk\3ds Max 2021\, PROCESSOR_ARCHITECTURE=AMD64, JDK-8=C:\Program Files\Java\jdk1.8.0_241, VSCODE_GIT_IPC_HANDLE=\\.\pipe\vscode-git-96623f9bce-sock, PSModulePath=D:\Documents\WindowsPowerShell\Modules;C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules;E:\Program Files\VisualSVN Server\PowerShellModules, RIDER_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\rider.vmoptions, DEVECOSTUDIO_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\devecostudio.vmoptions, GIT_ASKPASS=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass.sh, VISUALSVN_SERVER=E:\Program Files\VisualSVN Server\, CHROME_CRASHPAD_PIPE_NAME=\\.\pipe\crashpad_20100_HVDNLTAAZVITSIXE, CUDA_PATH_V12_1=F:\GPUToolkit\v12.1, APP_HOME=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\, DATASPELL_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\dataspell.vmoptions, ORIGINAL_XDG_CURRENT_DESKTOP=undefined, ProgramData=C:\ProgramData, ProgramW6432=C:\Program Files, BSPRINT_CLIENT=C:/Users/<USER>/AppData/Roaming/Brocadesoft, DATAGRIP_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\datagrip.vmoptions, LOCALAPPDATA=C:\Users\<USER>\AppData\Local, ChocolateyLastPathUpdate=133849427830057550, USERDOMAIN=PWRD-20230802BV, LOGONSERVER=\\PWRD-20230802BV, PYCHARM_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\pycharm.vmoptions, WIX=C:\Program Files (x86)\WiX Toolset v3.14\, JETBRAINSCLIENT_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\jetbrainsclient.vmoptions, LANG=en_US.UTF-8, =G:=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server, VSCODE_GIT_ASKPASS_MAIN=c:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\resources\app\extensions\git\dist\askpass-main.js, IDEA_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\idea.vmoptions, AQUA_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\aqua.vmoptions, RUBYMINE_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\rubymine.vmoptions, VSCODE_GIT_ASKPASS_EXTRA_ARGS=, CommonProgramFiles=C:\Program Files\Common Files, OS=Windows_NT, CUDA_PATH=F:\GPUToolkit\v12.1, NVM_HOME=E:\nvm, PROCESSOR_REVISION=9702, CLASSPATH=G:\WuLinWork\ProjectWuLin\snail\gm\gm-server\\gradle\wrapper\gradle-wrapper.jar, CommonProgramW6432=C:\Program Files\Common Files, SystemRoot=C:\Windows, TEMP=C:\Users\<USER>\AppData\Local\Temp, USERPROFILE=C:\Users\<USER>\Users\Administrator\AppData\Local\Temp, CommonProgramFiles(x86)=C:\Program Files (x86)\Common Files, PHPSTORM_VM_OPTIONS=D:\Downloads\jetbra\vmoptions\phpstorm.vmoptions}}]
15:57:19.562 [Test worker] DEBUG org.springframework.test.context.support.TestPropertySourceUtils - Adding inlined properties to environment: {spring.jmx.enabled=false, org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true, server.port=-1}
15:57:19.562 [Test worker] DEBUG org.springframework.core.env.StandardEnvironment - Adding PropertySource 'Inlined Test Properties' with highest search precedence

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::  (v2.0.2.BUILD-SNAPSHOT)

2025-06-09 15:57:19.772  INFO 66004 --- [    Test worker] c.w.gmserver.GmServerApplicationTests    : Starting GmServerApplicationTests on PWRD-20230802BV with PID 66004 (started by Administrator in G:\WuLinWork\ProjectWuLin\snail\gm\gm-server)
2025-06-09 15:57:19.773  INFO 66004 --- [    Test worker] c.w.gmserver.GmServerApplicationTests    : No active profile set, falling back to default profiles: default
2025-06-09 15:57:19.813  INFO 66004 --- [    Test worker] o.s.w.c.s.GenericWebApplicationContext   : Refreshing org.springframework.web.context.support.GenericWebApplicationContext@7a008e96: startup date [Mon Jun 09 15:57:19 CST 2025]; root of context hierarchy
2025-06-09 15:57:20.663  INFO 66004 --- [    Test worker] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'httpRequestHandlerAdapter' with a different definition: replacing [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration; factoryMethodName=httpRequestHandlerAdapter; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [org/springframework/boot/autoconfigure/web/servlet/WebMvcAutoConfiguration$EnableWebMvcConfiguration.class]] with [Root bean: class [null]; scope=; abstract=false; lazyInit=false; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration; factoryMethodName=httpRequestHandlerAdapter; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [org/springframework/data/rest/webmvc/config/RepositoryRestMvcConfiguration.class]]
2025-06-09 15:57:21.003  INFO 66004 --- [    Test worker] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration$$EnhancerBySpringCGLIB$$390be230] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-09 15:57:21.021  INFO 66004 --- [    Test worker] trationDelegate$BeanPostProcessorChecker : Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-09 15:57:21.022  INFO 66004 --- [    Test worker] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@5eae12e2' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-09 15:57:21.027  INFO 66004 --- [    Test worker] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration$$EnhancerBySpringCGLIB$$5de084e2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-09 15:57:21.035  INFO 66004 --- [    Test worker] trationDelegate$BeanPostProcessorChecker : Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-09 15:57:21.852  INFO 66004 --- [    Test worker] org.mongodb.driver.cluster               : Cluster created with settings {hosts=[localhost:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms', maxWaitQueueSize=500}
2025-06-09 15:57:21.941  INFO 66004 --- [localhost:27017] org.mongodb.driver.connection            : Opened connection [connectionId{localValue:1}] to localhost:27017
2025-06-09 15:57:21.946  INFO 66004 --- [localhost:27017] org.mongodb.driver.cluster               : Monitor thread successfully connected to server with description ServerDescription{address=localhost:27017, type=STANDALONE, state=CONNECTED, ok=true, version=ServerVersion{versionList=[8, 0, 9]}, minWireVersion=0, maxWireVersion=25, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=3356600}
2025-06-09 15:57:22.414  INFO 66004 --- [    Test worker] org.quartz.impl.StdSchedulerFactory      : Using default implementation for ThreadExecutor
2025-06-09 15:57:22.431  INFO 66004 --- [    Test worker] org.quartz.core.SchedulerSignalerImpl    : Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-09 15:57:22.431  INFO 66004 --- [    Test worker] org.quartz.core.QuartzScheduler          : Quartz Scheduler v.2.3.0 created.
2025-06-09 15:57:22.432  INFO 66004 --- [    Test worker] org.quartz.simpl.RAMJobStore             : RAMJobStore initialized.
2025-06-09 15:57:22.433  INFO 66004 --- [    Test worker] org.quartz.core.QuartzScheduler          : Scheduler meta-data: Quartz Scheduler (v2.3.0) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-06-09 15:57:22.433  INFO 66004 --- [    Test worker] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-06-09 15:57:22.433  INFO 66004 --- [    Test worker] org.quartz.impl.StdSchedulerFactory      : Quartz scheduler version: 2.3.0
2025-06-09 15:57:22.434  INFO 66004 --- [    Test worker] org.quartz.core.QuartzScheduler          : JobFactory set to: org.springframework.boot.autoconfigure.quartz.AutowireCapableBeanJobFactory@ac46498
2025-06-09 15:57:22.990  WARN 66004 --- [    Test worker] .c.m.RepositoryCollectionResourceMapping : @RestResource detected to customize the repository resource for com.wulin.gmserver.dao.FileDao! Use @RepositoryRestResource instead!
2025-06-09 15:57:23.507  INFO 66004 --- [    Test worker] o.s.s.web.DefaultSecurityFilterChain     : Creating filter chain: org.springframework.security.web.util.matcher.AnyRequestMatcher@1, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@315983c, org.springframework.security.web.context.SecurityContextPersistenceFilter@713daa8b, org.springframework.security.web.header.HeaderWriterFilter@6b4d902, org.springframework.security.web.authentication.logout.LogoutFilter@12168469, com.wulin.gmserver.security.RequestHeadAuthenticationFilter@221c540, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3245106d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7d54bc52, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@cae49d, org.springframework.security.web.session.SessionManagementFilter@66c879b0, org.springframework.security.web.access.ExceptionTranslationFilter@a2614b8, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5d340745]
2025-06-09 15:57:23.825  INFO 66004 --- [    Test worker] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**/favicon.ico] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-09 15:57:23.999  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerAdapter : Looking for @ControllerAdvice: org.springframework.web.context.support.GenericWebApplicationContext@7a008e96: startup date [Mon Jun 09 15:57:19 CST 2025]; root of context hierarchy
2025-06-09 15:57:24.084  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commands/{commandName}],methods=[POST]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.CommandController.command(java.lang.String,com.wulin.gmserver.controller.gm.CommandController$DoCommandParam) throws java.lang.Exception
2025-06-09 15:57:24.086  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commands/{commandId}],methods=[PATCH]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.CommandController.get(java.lang.String,java.util.Map<java.lang.String, java.lang.Boolean>)
2025-06-09 15:57:24.087  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commands/{commandName}],methods=[GET]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.CommandController.get(java.lang.String)
2025-06-09 15:57:24.087  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commands/{commandName}/schedule],methods=[POST]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.CommandController.commandSchedule(java.lang.String,com.wulin.gmserver.controller.gm.CommandController$DoCommandParam) throws java.lang.Exception
2025-06-09 15:57:24.087  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/commands/{commandName}/batch],methods=[POST],consumes=[multipart/form-data]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.CommandController.commandBatchProcess(java.lang.String,org.springframework.web.multipart.MultipartFile,java.lang.String) throws java.lang.Exception
2025-06-09 15:57:24.088  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gm/modules/PlayerOnlineGift/{id}],methods=[DELETE]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.PlayerOnlineGiftController.delete(java.lang.String)
2025-06-09 15:57:24.089  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gm/modules/PlayerOnlineGift],methods=[GET]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.PlayerOnlineGiftController.getAll(int,int)
2025-06-09 15:57:24.089  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gm/modules/PlayerOnlineGift],methods=[POST],consumes=[multipart/form-data]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.PlayerOnlineGiftController.playerOnlineGift(com.wulin.gmserver.domain.PlayerOnlineGiftDetail,org.springframework.web.multipart.MultipartFile) throws java.io.IOException
2025-06-09 15:57:24.090  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gm/modules/PlayerOnlineGift/{id}/csv],methods=[GET]}" onto public org.springframework.http.ResponseEntity<org.springframework.core.io.Resource> com.wulin.gmserver.controller.gm.PlayerOnlineGiftController.getCSV(java.lang.String)
2025-06-09 15:57:24.091  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gm/servers/{serverId}/updateCommands],methods=[GET]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.ServerController.get(int)
2025-06-09 15:57:24.091  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gm/servers],methods=[GET]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.ServerController.get()
2025-06-09 15:57:24.092  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/gm/servers/{wgsName}],methods=[GET]}" onto public java.lang.Object com.wulin.gmserver.controller.gm.ServerController.getByWgs(java.lang.String)
2025-06-09 15:57:24.093  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/menus/{id}],methods=[POST]}" onto public java.lang.Object com.wulin.gmserver.controller.MenuController.update(java.lang.String,com.wulin.gmserver.controller.MenuController$menuParam)
2025-06-09 15:57:24.094  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/menus/{id}],methods=[DELETE]}" onto public java.lang.Object com.wulin.gmserver.controller.MenuController.delete(java.lang.String)
2025-06-09 15:57:24.094  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/menus],methods=[POST]}" onto public java.lang.Object com.wulin.gmserver.controller.MenuController.create(com.wulin.gmserver.controller.MenuController$menuParam)
2025-06-09 15:57:24.094  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/menus],methods=[GET]}" onto public java.lang.Object com.wulin.gmserver.controller.MenuController.getAll()
2025-06-09 15:57:24.094  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/records],methods=[GET]}" onto public java.lang.Object com.wulin.gmserver.controller.OperationController.getAll(int,int)
2025-06-09 15:57:24.096  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/paramFilters],methods=[POST]}" onto public java.lang.Object com.wulin.gmserver.controller.ParamFilterController.create(com.wulin.gmserver.controller.ParamFilterController$ParamFilterData)
2025-06-09 15:57:24.096  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/roles/filters],methods=[POST]}" onto public java.lang.Object com.wulin.gmserver.controller.ParamFilterController.addToRole(com.wulin.gmserver.controller.ParamFilterController$AddData)
2025-06-09 15:57:24.096  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/roles/{roleId}/permissions],methods=[POST]}" onto public java.lang.Object com.wulin.gmserver.controller.PermissionController.delete(java.lang.String,java.util.Map<java.lang.String, java.lang.String>)
2025-06-09 15:57:24.100  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/jobs/{keyName}],methods=[DELETE]}" onto public void com.wulin.gmserver.controller.QuartzJobController.delete(java.lang.String) throws java.lang.Exception
2025-06-09 15:57:24.100  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/jobs],methods=[GET]}" onto public java.util.List<com.wulin.gmserver.controller.QuartzJobController$Job> com.wulin.gmserver.controller.QuartzJobController.getAll() throws java.lang.Exception
2025-06-09 15:57:24.101  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/users],methods=[POST]}" onto public java.lang.Object com.wulin.gmserver.controller.UserController.createOrUpdate(java.util.Map<java.lang.String, java.lang.String>)
2025-06-09 15:57:24.102  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/currentUser]}" onto public java.lang.Object com.wulin.gmserver.controller.UserController.currentUser()
2025-06-09 15:57:24.102  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/validParamCollections/{validParamCollectionId}],methods=[POST],consumes=[multipart/form-data]}" onto public java.lang.Object com.wulin.gmserver.controller.ValidParamCollectionController.update(java.lang.String,org.springframework.web.multipart.MultipartFile,java.lang.String) throws java.io.IOException
2025-06-09 15:57:24.102  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/validParamCollections],methods=[POST],consumes=[multipart/form-data]}" onto public java.lang.Object com.wulin.gmserver.controller.ValidParamCollectionController.create(java.lang.String,org.springframework.web.multipart.MultipartFile) throws java.io.IOException
2025-06-09 15:57:24.103  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/validParamCollections],methods=[POST]}" onto public java.lang.Object com.wulin.gmserver.controller.ValidParamCollectionController.create()
2025-06-09 15:57:24.105  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error]}" onto public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.error(javax.servlet.http.HttpServletRequest)
2025-06-09 15:57:24.105  INFO 66004 --- [    Test worker] s.w.s.m.m.a.RequestMappingHandlerMapping : Mapped "{[/error],produces=[text/html]}" onto public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
2025-06-09 15:57:24.139  INFO 66004 --- [    Test worker] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/webjars/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-09 15:57:24.139  INFO 66004 --- [    Test worker] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-09 15:57:24.139  INFO 66004 --- [    Test worker] o.s.w.s.handler.SimpleUrlHandlerMapping  : Mapped URL path [/api/browser/**] onto handler of type [class org.springframework.web.servlet.resource.ResourceHttpRequestHandler]
2025-06-09 15:57:24.157  INFO 66004 --- [    Test worker] .m.m.a.ExceptionHandlerExceptionResolver : Detected @ExceptionHandler methods in repositoryRestExceptionHandler
2025-06-09 15:57:24.240  INFO 66004 --- [    Test worker] o.s.b.a.w.s.WelcomePageHandlerMapping    : Adding welcome page: class path resource [static/index.html]
2025-06-09 15:57:24.323  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerAdapter   : Looking for @ControllerAdvice: org.springframework.web.context.support.GenericWebApplicationContext@7a008e96: startup date [Mon Jun 09 15:57:19 CST 2025]; root of context hierarchy
2025-06-09 15:57:24.335  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}],methods=[OPTIONS],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<?> org.springframework.data.rest.webmvc.RepositoryEntityController.optionsForCollectionResource(org.springframework.data.rest.webmvc.RootResourceInformation)
2025-06-09 15:57:24.335  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}],methods=[HEAD],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<?> org.springframework.data.rest.webmvc.RepositoryEntityController.headForItemResource(org.springframework.data.rest.webmvc.RootResourceInformation,java.io.Serializable,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler) throws org.springframework.web.HttpRequestMethodNotSupportedException
2025-06-09 15:57:24.336  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}],methods=[POST],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.RepositoryEntityController.postCollectionResource(org.springframework.data.rest.webmvc.RootResourceInformation,org.springframework.data.rest.webmvc.PersistentEntityResource,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler,java.lang.String) throws org.springframework.web.HttpRequestMethodNotSupportedException
2025-06-09 15:57:24.336  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}],methods=[PATCH],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.RepositoryEntityController.patchItemResource(org.springframework.data.rest.webmvc.RootResourceInformation,org.springframework.data.rest.webmvc.PersistentEntityResource,java.io.Serializable,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler,org.springframework.data.rest.webmvc.support.ETag,java.lang.String) throws org.springframework.web.HttpRequestMethodNotSupportedException,org.springframework.data.rest.webmvc.ResourceNotFoundException
2025-06-09 15:57:24.336  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}],methods=[GET],produces=[application/hal+json || application/json]}" onto public org.springframework.hateoas.Resources<?> org.springframework.data.rest.webmvc.RepositoryEntityController.getCollectionResource(org.springframework.data.rest.webmvc.RootResourceInformation,org.springframework.data.rest.webmvc.support.DefaultedPageable,org.springframework.data.domain.Sort,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler) throws org.springframework.data.rest.webmvc.ResourceNotFoundException,org.springframework.web.HttpRequestMethodNotSupportedException
2025-06-09 15:57:24.336  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}],methods=[DELETE],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<?> org.springframework.data.rest.webmvc.RepositoryEntityController.deleteItemResource(org.springframework.data.rest.webmvc.RootResourceInformation,java.io.Serializable,org.springframework.data.rest.webmvc.support.ETag) throws org.springframework.data.rest.webmvc.ResourceNotFoundException,org.springframework.web.HttpRequestMethodNotSupportedException
2025-06-09 15:57:24.336  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}],methods=[GET],produces=[application/x-spring-data-compact+json || text/uri-list]}" onto public org.springframework.hateoas.Resources<?> org.springframework.data.rest.webmvc.RepositoryEntityController.getCollectionResourceCompact(org.springframework.data.rest.webmvc.RootResourceInformation,org.springframework.data.rest.webmvc.support.DefaultedPageable,org.springframework.data.domain.Sort,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler) throws org.springframework.data.rest.webmvc.ResourceNotFoundException,org.springframework.web.HttpRequestMethodNotSupportedException
2025-06-09 15:57:24.337  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}],methods=[HEAD],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<?> org.springframework.data.rest.webmvc.RepositoryEntityController.headCollectionResource(org.springframework.data.rest.webmvc.RootResourceInformation,org.springframework.data.rest.webmvc.support.DefaultedPageable) throws org.springframework.web.HttpRequestMethodNotSupportedException
2025-06-09 15:57:24.337  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}],methods=[OPTIONS],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<?> org.springframework.data.rest.webmvc.RepositoryEntityController.optionsForItemResource(org.springframework.data.rest.webmvc.RootResourceInformation)
2025-06-09 15:57:24.337  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}],methods=[GET],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<org.springframework.hateoas.Resource<?>> org.springframework.data.rest.webmvc.RepositoryEntityController.getItemResource(org.springframework.data.rest.webmvc.RootResourceInformation,java.io.Serializable,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler,org.springframework.http.HttpHeaders) throws org.springframework.web.HttpRequestMethodNotSupportedException
2025-06-09 15:57:24.337  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}],methods=[PUT],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<? extends org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.RepositoryEntityController.putItemResource(org.springframework.data.rest.webmvc.RootResourceInformation,org.springframework.data.rest.webmvc.PersistentEntityResource,java.io.Serializable,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler,org.springframework.data.rest.webmvc.support.ETag,java.lang.String) throws org.springframework.web.HttpRequestMethodNotSupportedException
2025-06-09 15:57:24.339  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/search],methods=[OPTIONS],produces=[application/hal+json || application/json]}" onto public org.springframework.http.HttpEntity<?> org.springframework.data.rest.webmvc.RepositorySearchController.optionsForSearches(org.springframework.data.rest.webmvc.RootResourceInformation)
2025-06-09 15:57:24.339  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/search/{search}],methods=[GET],produces=[application/x-spring-data-compact+json]}" onto public org.springframework.hateoas.ResourceSupport org.springframework.data.rest.webmvc.RepositorySearchController.executeSearchCompact(org.springframework.data.rest.webmvc.RootResourceInformation,org.springframework.http.HttpHeaders,org.springframework.util.MultiValueMap<java.lang.String, java.lang.Object>,java.lang.String,java.lang.String,org.springframework.data.rest.webmvc.support.DefaultedPageable,org.springframework.data.domain.Sort,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler)
2025-06-09 15:57:24.339  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/search/{search}],methods=[OPTIONS],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> org.springframework.data.rest.webmvc.RepositorySearchController.optionsForSearch(org.springframework.data.rest.webmvc.RootResourceInformation,java.lang.String)
2025-06-09 15:57:24.339  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/search],methods=[GET],produces=[application/hal+json || application/json]}" onto public org.springframework.data.rest.webmvc.RepositorySearchesResource org.springframework.data.rest.webmvc.RepositorySearchController.listSearches(org.springframework.data.rest.webmvc.RootResourceInformation)
2025-06-09 15:57:24.339  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/search/{search}],methods=[GET],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<?> org.springframework.data.rest.webmvc.RepositorySearchController.executeSearch(org.springframework.data.rest.webmvc.RootResourceInformation,org.springframework.util.MultiValueMap<java.lang.String, java.lang.Object>,java.lang.String,org.springframework.data.rest.webmvc.support.DefaultedPageable,org.springframework.data.domain.Sort,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler,org.springframework.http.HttpHeaders)
2025-06-09 15:57:24.339  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/search],methods=[HEAD],produces=[application/hal+json || application/json]}" onto public org.springframework.http.HttpEntity<?> org.springframework.data.rest.webmvc.RepositorySearchController.headForSearches(org.springframework.data.rest.webmvc.RootResourceInformation)
2025-06-09 15:57:24.339  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/search/{search}],methods=[HEAD],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<java.lang.Object> org.springframework.data.rest.webmvc.RepositorySearchController.headForSearch(org.springframework.data.rest.webmvc.RootResourceInformation,java.lang.String)
2025-06-09 15:57:24.339  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/ || /api],methods=[OPTIONS],produces=[application/hal+json || application/json]}" onto public org.springframework.http.HttpEntity<?> org.springframework.data.rest.webmvc.RepositoryController.optionsForRepositories()
2025-06-09 15:57:24.340  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/ || /api],methods=[HEAD],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<?> org.springframework.data.rest.webmvc.RepositoryController.headForRepositories()
2025-06-09 15:57:24.340  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/ || /api],methods=[GET],produces=[application/hal+json || application/json]}" onto public org.springframework.http.HttpEntity<org.springframework.data.rest.webmvc.RepositoryLinksResource> org.springframework.data.rest.webmvc.RepositoryController.listRepositories()
2025-06-09 15:57:24.340  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}/{property}],methods=[PATCH || PUT || POST],consumes=[application/json || application/x-spring-data-compact+json || text/uri-list],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<? extends org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.RepositoryPropertyReferenceController.createPropertyReference(org.springframework.data.rest.webmvc.RootResourceInformation,org.springframework.http.HttpMethod,org.springframework.hateoas.Resources<java.lang.Object>,java.io.Serializable,java.lang.String) throws java.lang.Exception
2025-06-09 15:57:24.341  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}/{property}/{propertyId}],methods=[DELETE],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.RepositoryPropertyReferenceController.deletePropertyReferenceId(org.springframework.data.rest.webmvc.RootResourceInformation,java.io.Serializable,java.lang.String,java.lang.String) throws java.lang.Exception
2025-06-09 15:57:24.341  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}/{property}],methods=[GET],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.RepositoryPropertyReferenceController.followPropertyReference(org.springframework.data.rest.webmvc.RootResourceInformation,java.io.Serializable,java.lang.String,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler) throws java.lang.Exception
2025-06-09 15:57:24.341  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}/{property}/{propertyId}],methods=[GET],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.RepositoryPropertyReferenceController.followPropertyReference(org.springframework.data.rest.webmvc.RootResourceInformation,java.io.Serializable,java.lang.String,java.lang.String,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler) throws java.lang.Exception
2025-06-09 15:57:24.341  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}/{property}],methods=[GET],produces=[application/x-spring-data-compact+json || text/uri-list]}" onto public org.springframework.http.ResponseEntity<org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.RepositoryPropertyReferenceController.followPropertyReferenceCompact(org.springframework.data.rest.webmvc.RootResourceInformation,java.io.Serializable,java.lang.String,org.springframework.data.rest.webmvc.PersistentEntityResourceAssembler) throws java.lang.Exception
2025-06-09 15:57:24.341  INFO 66004 --- [    Test worker] o.s.d.r.w.RepositoryRestHandlerMapping   : Mapped "{[/api/{repository}/{id}/{property}],methods=[DELETE],produces=[application/hal+json || application/json]}" onto public org.springframework.http.ResponseEntity<? extends org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.RepositoryPropertyReferenceController.deletePropertyReference(org.springframework.data.rest.webmvc.RootResourceInformation,java.io.Serializable,java.lang.String) throws java.lang.Exception
2025-06-09 15:57:24.345  INFO 66004 --- [    Test worker] o.s.d.r.w.BasePathAwareHandlerMapping    : Mapped "{[/api/ || /api],methods=[GET],produces=[text/html]}" onto public org.springframework.web.servlet.View org.springframework.data.rest.webmvc.halbrowser.HalBrowser.index(javax.servlet.http.HttpServletRequest)
2025-06-09 15:57:24.345  INFO 66004 --- [    Test worker] o.s.d.r.w.BasePathAwareHandlerMapping    : Mapped "{[/api/browser],methods=[GET]}" onto public org.springframework.web.servlet.View org.springframework.data.rest.webmvc.halbrowser.HalBrowser.browser(javax.servlet.http.HttpServletRequest)
2025-06-09 15:57:24.346  INFO 66004 --- [    Test worker] o.s.d.r.w.BasePathAwareHandlerMapping    : Mapped "{[/api/profile],methods=[GET]}" onto org.springframework.http.HttpEntity<org.springframework.hateoas.ResourceSupport> org.springframework.data.rest.webmvc.ProfileController.listAllFormsOfMetadata()
2025-06-09 15:57:24.346  INFO 66004 --- [    Test worker] o.s.d.r.w.BasePathAwareHandlerMapping    : Mapped "{[/api/profile],methods=[OPTIONS]}" onto public org.springframework.http.HttpEntity<?> org.springframework.data.rest.webmvc.ProfileController.profileOptions()
2025-06-09 15:57:24.347  INFO 66004 --- [    Test worker] o.s.d.r.w.BasePathAwareHandlerMapping    : Mapped "{[/api/profile/{repository}],methods=[GET],produces=[application/alps+json || */*]}" onto org.springframework.http.HttpEntity<org.springframework.data.rest.webmvc.RootResourceInformation> org.springframework.data.rest.webmvc.alps.AlpsController.descriptor(org.springframework.data.rest.webmvc.RootResourceInformation)
2025-06-09 15:57:24.347  INFO 66004 --- [    Test worker] o.s.d.r.w.BasePathAwareHandlerMapping    : Mapped "{[/api/profile/{repository}],methods=[OPTIONS],produces=[application/alps+json]}" onto org.springframework.http.HttpEntity<?> org.springframework.data.rest.webmvc.alps.AlpsController.alpsOptions()
2025-06-09 15:57:24.347  INFO 66004 --- [    Test worker] o.s.d.r.w.BasePathAwareHandlerMapping    : Mapped "{[/api/profile/{repository}],methods=[GET],produces=[application/schema+json]}" onto public org.springframework.http.HttpEntity<org.springframework.data.rest.webmvc.json.JsonSchema> org.springframework.data.rest.webmvc.RepositorySchemaController.schema(org.springframework.data.rest.webmvc.RootResourceInformation)
2025-06-09 15:57:24.469  INFO 66004 --- [    Test worker] o.s.c.support.DefaultLifecycleProcessor  : Starting beans in phase 2147483647
2025-06-09 15:57:24.469  INFO 66004 --- [    Test worker] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-06-09 15:57:24.470  INFO 66004 --- [    Test worker] org.quartz.core.QuartzScheduler          : Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-06-09 15:57:24.479  INFO 66004 --- [    Test worker] c.w.gmserver.GmServerApplicationTests    : Started GmServerApplicationTests in 4.907 seconds (JVM running for 6.028)
2025-06-09 15:57:24.540  INFO 66004 --- [    Test worker] xio.Engine                               : xio.Engine started
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
