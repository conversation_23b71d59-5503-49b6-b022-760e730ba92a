import React, { Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import { Card, Divider, Modal, Table } from 'antd';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';
import moment from 'moment/moment'; // 确保 moment 被正确导入

const getValue = obj => Object.keys(obj)
  .map(key => obj[key])
  .join(',');

// 这两个变量在当前代码片段中未被使用，如果确实不需要可以移除
// const statusMap = ['default', 'processing', 'success', 'error'];
// const status = ['关闭', '运行中', '已上线', '异常'];

export default @connect(({ gm, loading }) => ({ // <--- 修改后的位置
  gm,
  loading: loading.models.rule, // loading.models.rule 似乎也未在组件中使用，可以考虑是否移除或正确使用
}))
class TableList extends PureComponent {
  state = {
    // modalVisible: false, // 这个状态似乎没有被使用，如果确实不需要可以移除
    // expandForm: false, // 这个状态似乎没有被使用，如果确实不需要可以移除
    visible: false, // 用于删除确认模态框
    selectedRecordId: null,
    // selectedRows: [], // 这个状态似乎没有被使用，如果确实不需要可以移除
    formValues: {}, // 用于存储表单筛选条件
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'gm/fetchPlayerOnlineGiftRecord',
      // 初始加载可以不带 payload，或带上默认的分页参数
      // payload: { currentPage: 1, pageSize: 10 },
    });
  }

  handleStandardTableChange = (pagination, filtersArg, sorter) => {
    const { dispatch } = this.props;
    const { formValues } = this.state;

    const filters = Object.keys(filtersArg)
      .reduce((obj, key) => {
        const newObj = { ...obj };
        newObj[key] = getValue(filtersArg[key]);
        return newObj;
      }, {});

    const params = {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      ...formValues,
      ...filters,
    };
    if (sorter.field) {
      // antd sorter.order 的值是 'ascend' 或 'descend'
      params.sorter = `${sorter.field}_${sorter.order === 'ascend' ? 'asc' : 'desc'}`;
    }

    dispatch({
      type: 'gm/fetchPlayerOnlineGiftRecord',
      payload: params,
    });
  };

  handleOk = () => {
    const { dispatch } = this.props;
    const { selectedRecordId } = this.state;
    if (selectedRecordId) { // 确保 selectedRecordId 存在
      dispatch({
        type: 'gm/deletePlayerOnlineGift',
        payload: { // 通常 dva 的 payload 是一个对象
            playerOnlineGiftId: selectedRecordId,
        },
        // callback: () => { // 成功删除后可以重新获取列表或进行其他操作
        //   // 例如，如果删除后希望列表刷新，可以在 dva model 的 effect 中处理，
        //   // 或者在这里再次 dispatch fetchPlayerOnlineGiftRecord
        //   // 但通常建议在 model 中处理数据联动
        // }
      });
    }
    this.setState({
      visible: false,
      selectedRecordId: null,
    });
  };

  handleCancel = () => {
    this.setState({
      visible: false,
      selectedRecordId: null,
    });
  };

  handleSelectedRecord = (recordId) => { // 参数名改为 recordId 更清晰
    this.setState({
      visible: true,
      selectedRecordId: recordId,
    });
  };

  // 详情功能 (如果需要)
  // handleDetailClick = (record) => {
  //   console.log('View details for:', record);
  //   // 这里可以设置一个 state 来显示详情模态框或跳转到详情页
  // };

  render() {
    const { gm: { playerOnlineGiftRecord }, loading } = this.props; // 从 props 中获取 loading
    const { visible, selectedRecordId } = this.state;

    const columns = [
      {
        title: 'ID', // 通常表头用大写或首字母大写
        dataIndex: 'id',
      },
      {
        title: '类型',
        dataIndex: ['detail', 'sendType'], // antd Table 支持路径 dataIndex
      },
      {
        title: '邮件标题',
        dataIndex: ['detail', 'title'],
      },
      {
        title: '领奖开始时间',
        dataIndex: ['detail', 'startTime'],
        render: val => val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-', // 添加空值判断
        sorter: true, // 如果后端支持按时间排序
      },
      {
        title: '领奖结束时间',
        dataIndex: ['detail', 'endTime'],
        render: val => val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '-', // 添加空值判断
        sorter: true, // 如果后端支持按时间排序
      },
      {
        title: '需求人',
        dataIndex: 'creator',
      },
      {
        title: '状态',
        dataIndex: 'deleted',
        render: val => <span style={{ color: val ? 'red' : 'green' }}>{val ? "已删除" : "正常"}</span>,
      },
      {
        title: '操作',
        render: (text, record) => ( // record 参数包含了当前行的数据
          <Fragment>
            {/* <a onClick={() => this.handleDetailClick(record)}>详情</a>
            <Divider type="vertical" /> */}
            {!record.deleted && ( // 如果未删除，才显示删除按钮
              <a onClick={() => this.handleSelectedRecord(record.id)} style={{ color: 'red' }}>删除</a>
            )}
          </Fragment>
        ),
      },
    ];

    // 确保 playerOnlineGiftRecord 和其属性存在，避免 undefined 错误
    const listData = playerOnlineGiftRecord && playerOnlineGiftRecord.list ? playerOnlineGiftRecord.list : [];
    const paginationProps = playerOnlineGiftRecord && playerOnlineGiftRecord.pagination ? {
        showSizeChanger: true,
        showQuickJumper: true,
        ...playerOnlineGiftRecord.pagination,
        current: playerOnlineGiftRecord.pagination.current || playerOnlineGiftRecord.pagination.currentPage, // 兼容 currentPage
    } : {
        current: 1,
        pageSize: 10,
        total: 0,
    };


    return (
      <PageHeaderLayout title="角色批量发奖记录">
        <Card bordered={false}> {/* 移除了 Card 的 title，因为 PageHeaderLayout 已有标题 */}
          <div>
            <Table
              rowKey={'id'}
              loading={loading} // 将 loading 状态传递给 Table
              dataSource={listData}
              columns={columns}
              pagination={paginationProps}
              onChange={this.handleStandardTableChange}
            />
          </div>
        </Card>

        <Modal
          title="删除确认"
          visible={visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          okText="确认删除"
          cancelText="取消"
          confirmLoading={loading} // 如果删除操作是异步的，可以显示加载状态
        >
          <p>您确定要删除这条记录吗？此操作不可恢复。</p>
          {/* 可以显示更详细的待删除信息，例如： <p>即将删除的记录 ID: {selectedRecordId}</p> */}
        </Modal>
      </PageHeaderLayout>
    );
  }
}