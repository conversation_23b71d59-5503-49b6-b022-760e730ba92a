
package wgs.msg.gm;

import com.goldhuman.Common.Marshal.Marshal;
import com.goldhuman.Common.Marshal.OctetsStream;
import com.goldhuman.Common.Marshal.MarshalException;

public class Param implements Marshal {
	public java.lang.String name;
	public java.lang.String desc;
	public java.lang.String paramtype;

	public Param() {
		name = "";
		desc = "";
		paramtype = "";
	}

	public Param(java.lang.String _name_, java.lang.String _desc_, java.lang.String _paramtype_) {
		this.name = _name_;
		this.desc = _desc_;
		this.paramtype = _paramtype_;
	}

	public final boolean _validator_() {
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		_os_.marshal(name, "UTF-16LE");
		_os_.marshal(desc, "UTF-16LE");
		_os_.marshal(paramtype, "UTF-16LE");
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		name = _os_.unmarshal_String("UTF-16LE");
		desc = _os_.unmarshal_String("UTF-16LE");
		paramtype = _os_.unmarshal_String("UTF-16LE");
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof Param) {
			Param _o_ = (Param)_o1_;
			if (!name.equals(_o_.name)) return false;
			if (!desc.equals(_o_.desc)) return false;
			if (!paramtype.equals(_o_.paramtype)) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += name.hashCode();
		_h_ += desc.hashCode();
		_h_ += paramtype.hashCode();
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append("T").append(name.length()).append(",");
		_sb_.append("T").append(desc.length()).append(",");
		_sb_.append("T").append(paramtype.length()).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

}

