import request from '../utils/request';

export async function queryPermission() {
  return request('/api/sysPermissions');
}

export async function createPermission(params) {
  return request('/api/sysPermissions', {
    method: 'POST',
    body: params,
  });
}

export async function editPermission(permissionId, params) {
  return request('/api/sysPermissions/' + permissionId, {
    method: 'PUT',
    body: params,
  });
}

export async function deletePermission(permissionId) {
  return request('/api/sysPermissions/' + permissionId, {
    method: 'DELETE',
  });
}
