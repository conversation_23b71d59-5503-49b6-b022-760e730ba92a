package com.wulin.gmserver.xio;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
public class XioInitializer implements ApplicationRunner {
    String xioxml = "gm.xio.xml";

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            xdb.Executor.start(1, 1, 1, 1, 2000);
            xio.XioConf.loadAndRegister(xioxml);
            xio.Engine.getInstance().open();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
