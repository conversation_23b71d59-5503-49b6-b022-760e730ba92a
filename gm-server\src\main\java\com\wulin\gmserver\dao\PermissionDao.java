package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.SysPermission;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.data.rest.core.annotation.RestResource;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Optional;

@RepositoryRestResource
public interface PermissionDao extends CrudRepository<SysPermission, String> {
    @RestResource
    @Override
    Optional<SysPermission> findById(String s);

    @RestResource
    @Override
    Iterable<SysPermission> findAll();

    @RestResource
    Optional<SysPermission> findByPermission(String permission);

    @PreAuthorize("hasRole('Admin')")
    @RestResource
    @Override
    void deleteById(String s);
}
