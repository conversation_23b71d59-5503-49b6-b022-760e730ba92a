package com.wulin.gmserver.domain;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "gm_module")
public class GMModule {
    @Id
    private String id;
    private String name;
    private String desc;
    @DBRef
    private List<Command> commands = new ArrayList<>();
}
