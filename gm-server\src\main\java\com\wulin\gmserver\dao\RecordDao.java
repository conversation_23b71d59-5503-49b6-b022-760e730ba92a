package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.Record;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.data.rest.core.annotation.RestResource;

import java.util.List;
import java.util.Optional;

@RepositoryRestResource
public interface RecordDao extends MongoRepository<Record, String> {
    @RestResource
    @Override
    List<Record> findAll(Sort sort);

    @RestResource
    @Override
    Optional<Record> findById(String s);

    @RestResource
    @Override
    Page<Record> findAll(Pageable pageable);

    @RestResource
    @Override
    <S extends Record> Page<S> findAll(Example<S> example, Pageable pageable);

    @RestResource
    @Override
    <S extends Record> List<S> findAll(Example<S> example);

    @RestResource
    @Override
    <S extends Record> List<S> findAll(Example<S> example, Sort sort);
}
