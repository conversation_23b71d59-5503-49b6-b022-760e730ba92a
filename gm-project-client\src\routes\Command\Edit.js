import React, { Component } from 'react';
import { connect } from 'dva';
import { Button, Checkbox, Divider, Form, Select, Tree, } from 'antd';

const TreeNode = Tree.TreeNode;

export default @connect(({ gm, loading }) => ({
  gm,
  loading: loading.models.rule,
}))
@Form.create()
class EditCommand extends Component {
  state = {};

  handleRequestServerChange = (command, e) => {
    this.props.dispatch({
      type: 'gm/patchCommandById',
      payload: {
        requestServer: e.target.checked
      },
      commandId: command.id,
    });
  };
  handleNeedLogChange = (command, e) => {
    this.props.dispatch({
      type: 'gm/patchCommandById',
      payload: {
        needRecord: e.target.checked
      },
      commandId: command.id,
    });
  };
  handleUpdateCommands = (e) => {
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (!err) {
        this.props.dispatch({
          type: 'gm/updateCommands',
          serverId: values.serverId,
        })
      }
    });
  };

  componentDidMount() {
    this.props.dispatch({
      type: 'gm/fetchServers',
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { gm: { modules, servers }, loading } = this.props;
    const moduleTreeNode = module => (
      <TreeNode key={module.id} title={module.desc}>
        {module.commands && module.commands.length > 0 ? module.commands.map(commandTreeNode) : null}
      </TreeNode>);


    const commandTreeNode = command => {
      return <TreeNode key={command.id} title={
        <div>{command.desc}
          <Divider type="vertical"/>
          <Checkbox onChange={checkedValue => this.handleNeedLogChange(command, checkedValue)}
                    defaultChecked={command.needRecord}>是否记录操作日志</Checkbox>
          <Divider type="vertical"/>
          <Checkbox onChange={checkedValue => this.handleRequestServerChange(command, checkedValue)}
                    defaultChecked={command.requestServer}>是否单独服务器执行</Checkbox>
        </div>
      }/>;
    };

    const serverItems = servers.sort((a, b) => a.serverId - b.serverId)
      .map((server, index) =>
        <Select.Option key={index} value={server.serverId}>
          {server.name.length === 0 ? '服务器' + server.serverId : server.name + '(' + server.serverId + ')'}
        </Select.Option>);


    return (
      <>
        <Tree>
          {modules.map(moduleTreeNode)}
        </Tree>
        <Form onSubmit={this.handleUpdateCommands}>
          <Form.Item>
            {getFieldDecorator('serverId', {
              rules: [{ required: true, message: '请选择服务器' }],
            })(
              <Select allowClear={true}
                      placeholder="请选择服务器"
                      filterOption={(input, option) => option.props.value.toString()
                        .indexOf(input) === 0}
              >
                {serverItems}
              </Select>
            )}
          </Form.Item>
          <Form.Item>
            <Button type='primary' htmlType='submit'>
              提交
            </Button>
          </Form.Item>
        </Form>
      </>

    );
  }
}
