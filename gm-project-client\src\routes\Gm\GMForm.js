import React, { Component } from 'react';
import { <PERSON><PERSON>, Card, Col, Form, Input, Row, Select } from 'antd';
import { connect } from 'dva';

@connect(({ global, gm, loading }) => ({
  collapsed: global.collapsed,
  submitting: loading.effects['gm/doCommand'],
  gm: gm,
}))
@Form.create()
class GMForm extends Component {
  state = {
    command: {
      params: [],
      name: '加载中',
      desc: '加载中...',
    },
    selectedSeverIds: []
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (!err) {
        let servers = this.props.gm.servers;
        let selected_servers = this.state.command.requestServer ? [values.servers] : values.servers;
        let serverIds = selected_servers.map(serverId => {
          let s = servers.find(server => server.serverId === serverId);
          return s && s.id
        });
        this.props.dispatch({
          type: 'gm/doCommand',
          payload: {
            data: {
              params: values && values.params || [],
              roleId: values && values.roleId,
              serverIds: serverIds
            },
          },
          commandName: this.state.command.name,
        })
          .then(this.handleCallBack);

        this.setState({
          commandResult: null
        });
      }
    });

  };

  handleCallBack = data => {
    this.setState({
      commandResult: data === true ? '执行成功' : data
    });
  };

  handleSelectAllServers = () => {
    const { gm: { servers }, form } = this.props;
    form.setFieldsValue({ servers: servers.map(server => server.serverId) });
    // this.setState({
    //   selectedSeverIds: servers.map(server=>server.id)
    // })
  };

  componentDidMount() {
    this.props.dispatch({
      type: 'gm/fetchCommandById',
      commandId: this.props.match.params.commandId,
    })
      .then(command => this.setState({ command: command }));

    this.props.dispatch({
      type: 'gm/fetchServers',
    });
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.match.params.commandId && this.props.match.params.commandId !== nextProps.match.params.commandId) {
      this.props.dispatch({
        type: 'gm/fetchCommandById',
        commandId: nextProps.match.params.commandId,
      })
        .then(command => this.setState({ command: command, commandResult: null }));
    }
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { submitting, gm: { servers } } = this.props;
    const { command, selectedSeverIds } = this.state;
    const formItems = command.params.length > 0 ?
      command.params.map((p, index) => (
        <Form.Item key={index}>
          {getFieldDecorator('params.' + index, {
            rules: [{ required: true, message: '不能为空' }],
          })(
            <Input id={p.name} placeholder={p.desc}/>
          )}

        </Form.Item>
      )) : null;

    const roleItem = command.withRole ? (<Form.Item>
      {getFieldDecorator('roleId', {
        rules: [{ required: true, message: '角色Id不能为空' }],
      })(
        <Input id={'roleId'} placeholder={'角色Id'}/>
      )}
    </Form.Item>) : null;

    const commandResult = this.state.commandResult;
    const renderCommandResult = commandResult !== undefined && commandResult !== null ? commandResult.sort((a,b)=>a.serverId - b.serverId).map(a => (
      <Row gutter={16} key={a.serverId}>
        <Col span={6}>{a.serverId}</Col>
        <Col span={18}>
          <pre>{a.result}</pre>
        </Col>
      </Row>
    )) : null;

    const serverItems = servers.sort((a,b)=>a.serverId - b.serverId).map((server, index) =>
      <Select.Option key={index} value={server.serverId}>
        {server.name.length === 0 ? '服务器' + server.serverId : server.name + '(' + server.serverId + ')'}
      </Select.Option>);

    const ServerSelect = command.requestServer ? (
      <Form.Item>
        {getFieldDecorator('servers', {
          rules: [{ required: true, message: '请选择服务器' }],
        })(
          <Select style={{ maxWidth: 286, width: '100%' }} allowClear={true}
                  placeholder="该命令需要指定服务器运行"
                  filterOption={(input, option) => option.props.value.toString()
                    .indexOf(input) === 0}
          >
            {serverItems}
          </Select>
        )}
      </Form.Item>
    ) : (
      <Form.Item>
        {/*<Row gutter={8}>*/}
        {/*<Col span={12}>*/}
        {getFieldDecorator('servers', {
          rules: [{ required: true, message: '请选择服务器' }],
        })(
          <Select style={{ maxWidth: 1000, width: '100%' }} mode={'multiple'} allowClear={true}
                  placeholder="请选择服务器(可多选)"
                  filterOption={(input, option) => option.props.value.toString()
                    .indexOf(input) === 0}
          >
            {serverItems}
          </Select>
        )}
        {/*</Col>*/}
        {/*<Col span={12}>*/}
        <Button onClick={this.handleSelectAllServers}>全选</Button>
        {/*</Col>*/}
        {/*</Row>*/}
      </Form.Item>
    );

    return (
      <>
        <Card title={command.desc + '(' + command.name + ')'} bordered={false}>
          <Form onSubmit={this.handleSubmit}>
            {roleItem}
            {formItems}
            <Card bordered={false} bodyStyle={{ padding: 0 }}>
              {ServerSelect}
            </Card>

            <Form.Item>
              <Button type='primary' htmlType='submit' loading={submitting}>
                提交
              </Button>
            </Form.Item>
          </Form>
        </Card>

        <Card title="执行结果" bordered={false}>
          <div>
            {renderCommandResult}
          </div>
        </Card>
      </>
    );

  };
}

export default GMForm;
