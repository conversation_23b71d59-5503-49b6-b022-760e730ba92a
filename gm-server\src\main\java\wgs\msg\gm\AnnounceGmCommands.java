
package wgs.msg.gm;

import com.goldhuman.Common.Marshal.OctetsStream;
import com.goldhuman.Common.Marshal.MarshalException;

// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS


// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class AnnounceGmCommands extends xio.Protocol {
	@Override
	protected void process() {
		// protocol handle
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 721597;

	public int getType() {
		return 721597;
	}

	public java.util.LinkedList<wgs.msg.gm.Module> modules;
	public int serverid;

	public AnnounceGmCommands() {
		modules = new java.util.LinkedList<wgs.msg.gm.Module>();
	}

	public AnnounceGmCommands(java.util.LinkedList<wgs.msg.gm.Module> _modules_, int _serverid_) {
		this.modules = _modules_;
		this.serverid = _serverid_;
	}

	public final boolean _validator_() {
		for (wgs.msg.gm.Module _v_ : modules)
			if (!_v_._validator_()) return false;
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		_os_.compact_uint32(modules.size());
		for (wgs.msg.gm.Module _v_ : modules) {
			_os_.marshal(_v_);
		}
		_os_.marshal(serverid);
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		for (int _size_ = _os_.uncompact_uint32(); _size_ > 0; --_size_) {
			wgs.msg.gm.Module _v_ = new wgs.msg.gm.Module();
			_v_.unmarshal(_os_);
			modules.add(_v_);
		}
		serverid = _os_.unmarshal_int();
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof AnnounceGmCommands) {
			AnnounceGmCommands _o_ = (AnnounceGmCommands)_o1_;
			if (!modules.equals(_o_.modules)) return false;
			if (serverid != _o_.serverid) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += modules.hashCode();
		_h_ += serverid;
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(modules).append(",");
		_sb_.append(serverid).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

