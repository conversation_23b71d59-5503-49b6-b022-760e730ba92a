package com.wulin.gmserver.service;

import com.wulin.gmserver.dao.CommandDao;
import com.wulin.gmserver.dao.GMModuleDao;
import com.wulin.gmserver.dao.MenuDao;
import com.wulin.gmserver.dao.PermissionDao;
import com.wulin.gmserver.domain.Command;
import com.wulin.gmserver.domain.GMModule;
import com.wulin.gmserver.domain.Menu;
import com.wulin.gmserver.domain.SysPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GMModuleService {
    @Autowired
    CommandService commandService;

    @Autowired
    GMModuleDao gmModuleDao;

    @Autowired
    MenuDao menuDao;

    @Autowired
    CommandDao commandDao;

    @Autowired
    PermissionDao permissionDao;

    public void createOrUpdate(GMModule gmModule) {
        Menu GM_MENU = menuDao.findById("GM_MENU").orElseGet(()->{
            Menu m = new Menu();
            m.setId("GM_MENU");
            m.setName("GM指令");
            m.setPath("");
            menuDao.save(m);
            Menu menuRoot = menuDao.findById("DEFAULT_MENU").get();
            menuRoot.getChildren().add(m);
            menuDao.save(menuRoot);
            return m;
        });

        Menu menu = menuDao.findById("GM_MODULE_"+gmModule.getName()).orElseGet(()->{
            Menu m = new Menu();
            m.setId("GM_MODULE_"+gmModule.getName());
            m.setPath("");
            m.setName(gmModule.getName() + "(" + gmModule.getDesc() + ")");
            GM_MENU.getChildren().add(m);
            return m;
        });
        menu.setName(gmModule.getName() + "(" + gmModule.getDesc() + ")");
        for (Command command : gmModule.getCommands()) {
            commandDao.findByName(command.getName()).ifPresent(c -> {
                command.setRequestServer(c.isRequestServer());
                command.setNeedRecord(c.isNeedRecord());
            });
            command.setId(command.getName());
            commandDao.save(command);
            SysPermission permission = permissionDao.findByPermission(command.getName()).orElseGet(()->{
                SysPermission p = new SysPermission();
                p.setName(command.getDesc());
                p.setPermission(command.getName());
                permissionDao.save(p);
                return p;
            });
//        不存在该指令菜单，创建
            Menu commandMenu = menuDao.findById("GM_COMMAND_" + command.getName()).orElseGet(()->{
                Menu m = new Menu();
                m.setId("GM_COMMAND_" + command.getName());
                m.setName(command.getName() + "(" + command.getDesc() + ")");
                m.setPath("/commands/" + command.getName());
                return m;
            });
            commandMenu.setName(command.getName() + "(" + command.getDesc() + ")");
            if(commandMenu.getPermission() == null){
                commandMenu.setPermission(permission);
            }
            menuDao.save(commandMenu);
            if(!menu.getChildren().contains(commandMenu)){
                menu.getChildren().add(commandMenu);
            }
        }
        menuDao.save(menu);
        menuDao.save(GM_MENU);
    }
}
