package com.wulin.gmserver.controller;

import com.wulin.gmserver.dao.MenuDao;
import com.wulin.gmserver.domain.Menu;
import com.wulin.gmserver.domain.SysPermission;
import com.wulin.gmserver.security.MyUserDetails;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
public class MenuController {
    private final static String DEFAULT_MENU = "DEFAULT_MENU";

    @Autowired
    MenuDao menuDao;

    @RequestMapping(value = "/menus", method = RequestMethod.GET)
    public Object getAll() {
        List<Menu> menus = menuDao.findById(DEFAULT_MENU).get().getChildren();
        MyUserDetails userDetails = (MyUserDetails) SecurityContextHolder.getContext()
                .getAuthentication()
                .getPrincipal();
        Collection<GrantedAuthority> grantedAuthorities = userDetails.getAuthorities();
        return menus.stream().map(m->makeMenu(m, grantedAuthorities)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static Menu makeMenu(Menu menu, Collection<GrantedAuthority> grantedAuthorities) {
        if(checkGrant("ROLE_Admin", grantedAuthorities)){
            return menu;
        }
        if (menu.getPermission() != null) {
            SysPermission permission = menu.getPermission();
            if (!checkGrant(permission.getPermission(), grantedAuthorities))
                return null;
        }
        List<Menu> children = new ArrayList<>();
        for (Menu child : menu.getChildren()) {
            Menu c = makeMenu(child, grantedAuthorities);
            if(c != null)
                children.add(c);
        }
        if (children.isEmpty() && StringUtils.isEmpty(menu.getPath())) {
            return null;
        }
        menu.setChildren(children);
        return menu;
    }

    private static boolean checkGrant(String permission, Collection<GrantedAuthority> grantedAuthorities) {
        for (GrantedAuthority grantedAuthority : grantedAuthorities) {
            if (permission.equals(grantedAuthority.getAuthority()))
                return true;
        }
        return false;
    }

    @Data
    static class menuParam {
        String name;
        String path;
        String parentId;
    }

    @PreAuthorize("hasRole('Admin')")
    @RequestMapping(value = "/menus/{id}", method = RequestMethod.POST)
    public Object update(@PathVariable("id") String id, @RequestBody menuParam param) {
        Menu menu = menuDao.findById(id).get();
        if (param.getName() != null) {
            menu.setName(param.getName());
        }
        menu.setPath(param.getPath());
        menuDao.save(menu);
        return getAll();
    }

    @PreAuthorize("hasRole('Admin')")
    @RequestMapping(value = "/menus", method = RequestMethod.POST)
    public Object create(@RequestBody menuParam param) {
        Menu parentMenu;
        if (param.getParentId() == null || param.getParentId().isEmpty()) {
            parentMenu = menuDao.findById(DEFAULT_MENU).get();
        } else {
            parentMenu = menuDao.findById(param.getParentId()).get();
        }

        Menu menu = new Menu();
        menu.setName(param.getName());
        menu.setPath(param.getPath());
        menuDao.save(menu);
        List<Menu> children = parentMenu.getChildren();
        children.add(menu);
        menuDao.save(parentMenu);
        return getAll();
    }

    @PreAuthorize("hasRole('Admin')")
    @RequestMapping(value = "/menus/{id}", method = RequestMethod.DELETE)
    public Object delete(@PathVariable("id") String id) {
        menuDao.deleteById(id);
        return getAll();
    }
}
