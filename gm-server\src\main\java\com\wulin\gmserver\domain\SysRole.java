package com.wulin.gmserver.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wulin.gmserver.domain.paramfilter.AbstractParamFilter;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.*;

@Data
public class SysRole {
    @Id
    private String id;
    private String roleName;
    private String roleDesc;

//    private Date createTime;
//    private Date updateTime;

    @DBRef(lazy = true)
    private Set<SysPermission> permissions = new HashSet<>();

    @JsonIgnore
    private Map<String, ParamWithFilters> commandFilter = new HashMap<>();

    @Data
    public static class AbstractParamFilterList {
        @DBRef()
        List<AbstractParamFilter> filters = new ArrayList<>();
    }

    @Data
    public static class ParamWithFilters {
        Map<String, AbstractParamFilterList> paramFilters = new HashMap<>();
    }
}
