package com.wulin.gmserver.controller;

import com.wulin.gmserver.dao.ValidParamCollectionDao;
import com.wulin.gmserver.domain.ValidParamCollection;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
public class ValidParamCollectionController {
    @Autowired
    ValidParamCollectionDao validParamCollectionDao;

    @RequestMapping(value = "/validParamCollections", method = RequestMethod.POST)
    public Object create() {
        return true;
    }

    @Data
    private static class ValidParamCollectionData {
        private Set<Integer> collection = new HashSet<>();
        private String name;
        private String type;
    }

    @RequestMapping(value = "/validParamCollections", consumes = "multipart/form-data", method = RequestMethod.POST)
    public Object create(String name, @RequestPart("file") MultipartFile file) throws IOException {
        HashSet<Integer> params = getParamsByFile(file);
        ValidParamCollection validParamCollection = new ValidParamCollection();
        validParamCollection.setName(name);
        validParamCollection.setValidParams(params);
        validParamCollectionDao.save(validParamCollection);
        return validParamCollection;
    }

    @RequestMapping(value = "/validParamCollections/{validParamCollectionId}", consumes = "multipart/form-data", method = RequestMethod.POST)
    public Object update(String name, @RequestPart("file") MultipartFile file, @PathVariable String validParamCollectionId) throws IOException {
        HashSet<Integer> params = getParamsByFile(file);
        ValidParamCollection validParamCollection = validParamCollectionDao.findById(validParamCollectionId).get();
        validParamCollection.setName(name);
        validParamCollection.setValidParams(params);
        validParamCollectionDao.save(validParamCollection);
        return validParamCollection;
    }

    private static HashSet<Integer> getParamsByFile(MultipartFile file) throws IOException {
        byte[] bytes = file.getBytes();
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
        HashSet<Integer> params = new HashSet<>();
        try (BufferedReader bufferedReader = new BufferedReader(inputStreamReader)) {
            List<String> lines = bufferedReader.lines().collect(Collectors.toList());
            for (String line : lines) {
                String[] ss = line.split(",");
                String args0 = ss[0];
                int id = Integer.parseInt(args0);
                params.add(id);
            }
        }
        return params;
    }
}
