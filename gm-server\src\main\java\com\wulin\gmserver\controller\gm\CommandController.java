package com.wulin.gmserver.controller.gm;

import com.wulin.gmserver.dao.RecordDao;
import com.wulin.gmserver.dao.ServerDao;
import com.wulin.gmserver.domain.Command;
import com.wulin.gmserver.domain.User;
import com.wulin.gmserver.domain.gm.Server;
import com.wulin.gmserver.security.MyUserDetails;
import com.wulin.gmserver.service.CommandService;
import com.wulin.gmserver.service.ScheduleService;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStreamReader;
import java.io.Reader;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("commands")
public class CommandController {
    @Autowired
    CommandService commandService;
    @Autowired
    ServerDao serverDao;

    @Autowired
    RecordDao recordDao;

    @Autowired
    ScheduleService scheduleService;

@Data
    private static class DoCommandParam {
        private Long roleId;
        private String commandName;
        private String wgs;
        private List<String> params;
        private List<String> serverIds;
        private Long startTime;
    }

    private static class BatchProcessCommand {
        private Long roleId;
        private List<String> params;
        private List<Server> servers;
    }
    @RequestMapping(value = "/{commandName}", method = RequestMethod.POST)
    public Object command(@PathVariable String commandName, @RequestBody DoCommandParam param) throws Exception {
        Command command = commandService.commandDao.findByName(commandName).orElse(null);
        return callCommand(command, param.getServerIds(), param.getParams(), param.getRoleId(), param.getWgs());
    }

    @RequestMapping(value = { "/{commandName}/schedule" }, method = { RequestMethod.POST })
    public Object commandSchedule(@PathVariable String commandName, @RequestBody DoCommandParam param)
            throws Exception {
        Command command = commandService.commandDao.findByName(commandName).orElse(null);
        MyUserDetails userDetails = (MyUserDetails) SecurityContextHolder.getContext().getAuthentication()
                .getPrincipal();
        User user = userDetails.getUser();
        if (!commandService.checkAuth(command, user, param.getParams())) {
            return Boolean.valueOf(false);
        }
        scheduleService.create(commandName, param.getServerIds(), param.getParams(), param.getRoleId(), param
                .getWgs(), user.getUserName(), param.getStartTime());
        return Boolean.valueOf(true);
    }

    @RequestMapping(value = { "/{commandName}/batch" }, consumes = { "multipart/form-data" }, method = {
            RequestMethod.POST })
    public Object commandBatchProcess(@PathVariable String commandName, @RequestPart("file") MultipartFile file,
            @RequestParam String wgs) throws Exception {
        Reader reader = new InputStreamReader(file.getInputStream());
        CSVParser parser = CSVFormat.DEFAULT.withAllowMissingColumnNames().parse(reader);
        Command command = commandService.commandDao.findByName(commandName).get();
        Iterable<Server> allServerList = serverDao.findAllByWgsName(wgs);
        HashMap<Integer, Server> allServerMap = new HashMap<>();
        for (Server server : allServerList) {
            allServerMap.put(server.getServerId(), server);
        }
        List<BatchProcessCommand> batch = new ArrayList<>();
        for (CSVRecord record : parser) {
            List<Integer> serverIds;
            BatchProcessCommand c = new BatchProcessCommand();

            try {
                serverIds = (List<Integer>) Arrays.<String>stream(record.get(0).split(";")).map(String::trim)
                        .map(Integer::parseInt).collect(Collectors.toList());
            } catch (Exception e) {
                return "服务器ID类型错误";
            }
            List<String> params = new ArrayList<>();
            int size = record.size();
            if (command.isWithRole()) {
                try {
                    c.roleId = Long.valueOf(Long.parseLong(record.get(1).trim()));
                } catch (Exception e) {
                    return "角色ID转换错误";
                }
            }
            for (int j = 1; j < size; j++) {
                params.add(record.get(j));
            }
            c.params = params;
            c.servers = (List) serverIds.stream().map(allServerMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            if (c.servers.isEmpty()) {
                return "服务器ID不存在:" + serverIds;
            }
            batch.add(c);
        }
        MyUserDetails userDetails = (MyUserDetails) SecurityContextHolder.getContext().getAuthentication()
                .getPrincipal();
        User user = userDetails.getUser();
        int i;
        for (i = 0; i < batch.size(); i++) {
            BatchProcessCommand element = batch.get(i);
            try {
                if (!commandService.checkAuth(command, user, element.params)) {
                    return MessageFormat.format("无权限执行第{0}条命令", new Object[] { Integer.valueOf(i + 1) });
                }
            } catch (Exception e) {
                return MessageFormat.format("第{0}条命令检验错误", new Object[] { Integer.valueOf(i + 1) });
            }
        }

        for (i = 0; i < batch.size(); i++) {
            BatchProcessCommand element = batch.get(i);

            try {
                List list = commandService.callCommand(element.servers, command, element.params, wgs, element.roleId, user);
            } catch (Exception e) {
                return MessageFormat.format("第{0}条命令执行错误，已执行{1}条命令",
                        new Object[] { Integer.valueOf(i + 1), Integer.valueOf(i) });
            }
        }
        return MessageFormat.format("执行完成，共执行{0}条命令", new Object[] { Integer.valueOf(batch.size()) });
    }

    private Object callCommand(Command command, List<String> serverIds, List<String> params, Long roleId, String wgs) {
        Iterable<Server> servers;
     MyUserDetails userDetails = (MyUserDetails)SecurityContextHolder.getContext().getAuthentication().getPrincipal();
     User user = userDetails.getUser();
     if (!commandService.checkAuth(command, user, params)) {
       return Boolean.valueOf(false);
     }
 
     
     if (serverIds == null || serverIds.isEmpty()) {
       servers = serverDao.findAllByWgsName("WgsdConnector");
     } else {
       servers = serverDao.findAllById(serverIds);
     } 
     List<Map<String, Object>> result = commandService.callCommand(servers, command, params, wgs, roleId, user);
     return result;
    }

    @RequestMapping(value = "/{commandName}", method = RequestMethod.GET)
    public Object get(@PathVariable String commandName) {
        Command command = commandService.commandDao.findByName(commandName).get();
        return command;
    }

    @RequestMapping(value = "/{commandId}", method = RequestMethod.PATCH)
    public Object get(@PathVariable String commandId, @RequestBody Map<String, Boolean> fields) {
        Command command = commandService.commandDao.findById(commandId).get();
        Boolean needRecord = fields.get("needRecord");
        Boolean requestServer = fields.get("needRecord");
        if (needRecord != null) {
            command.setNeedRecord(needRecord);
        }
        if (requestServer != null) {
            command.setRequestServer(requestServer);
        }
        commandService.commandDao.save(command);
        return command;
    }

}
