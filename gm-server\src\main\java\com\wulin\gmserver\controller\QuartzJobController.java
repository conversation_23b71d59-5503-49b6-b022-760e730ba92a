package com.wulin.gmserver.controller;

import com.wulin.gmserver.dao.ServerDao;
import com.wulin.gmserver.domain.gm.Server;
import com.wulin.gmserver.service.ScheduleService;
import com.wulin.gmserver.service.ScheduleService.JobDetailAndTrigger;
import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Trigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/jobs")
public class QuartzJobController {

    @Autowired
    ScheduleService scheduleService;

    @Autowired
    ServerDao serverDao;

    /**
     * 获取所有定时任务信息
     */
    @GetMapping
    public List<Job> getAll() throws Exception {
        List<JobDetailAndTrigger> jobDetails = scheduleService.findAll();
        List<Job> jobs = new ArrayList<>();

        for (JobDetailAndTrigger jobDetailAndTrigger : jobDetails) {
            Job job = new Job();
            JobDataMap dataMap = jobDetailAndTrigger.jobDetail.getJobDataMap();
            jobDetailAndTrigger.triggers.get(0).getStartTime();
            job.commandName = dataMap.getString("commandName");
            job.wgs = dataMap.getString("wgs");
            job.userName = dataMap.getString("userName");
            job.roleId = (Long) dataMap.get("roleId");

            List<String> serverIds = (List<String>) dataMap.get("serverIds");
            List<Server> servers = serverDao.findAllById(serverIds);
            job.serverIds = servers.stream().map(Server::getServerId).collect(Collectors.toList());

            job.params = (List<String>) dataMap.get("params");
            job.triggerTime = new ArrayList<>(1);

            job.keyName = jobDetailAndTrigger.jobDetail.getKey().getName();
            for (Trigger trigger : jobDetailAndTrigger.triggers) {
                job.triggerTime.add(trigger.getStartTime().getTime());
            }

            jobs.add(job);
        }

        return jobs;
    }

    /**
     * 删除指定 Job
     */
    @DeleteMapping("/{keyName}")
    public void delete(@PathVariable String keyName) throws Exception {
        scheduleService.delete(new JobKey(keyName));
    }

    /**
     * 内部静态类：用于封装任务展示数据
     */
    public static class Job {
        public String commandName;
        public String wgs;
        public String userName;
        public Long roleId;
        public List<Integer> serverIds;
        public List<String> params;
        public List<Long> triggerTime;
        public String keyName;
    }
}
