import { queryRecord } from '../services/record';

export default {
  namespace: 'record',

  state: {
    data: {
      list: [],
      pagination: {},
    },
  },

  effects: {
    * fetchRecord({ payload }, { call, put }) {
      const response = yield call(queryRecord, payload);
      yield put({
        type: 'saveRecord',
        payload: response,
      });
    },
  },

  reducers: {
    saveRecord(state, action) {
      return {
        ...state,
        data: action.payload,
      };
    },
  },
};
