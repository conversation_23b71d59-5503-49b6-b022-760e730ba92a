package com.wulin.gmserver.controller;

import com.wulin.gmserver.dao.CommandDao;
import com.wulin.gmserver.dao.ParamFilterDao;
import com.wulin.gmserver.dao.SysRoleDao;
import com.wulin.gmserver.dao.ValidParamCollectionDao;
import com.wulin.gmserver.domain.Command;
import com.wulin.gmserver.domain.SysRole;
import com.wulin.gmserver.domain.ValidParamCollection;
import com.wulin.gmserver.domain.paramfilter.*;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

@RestController
public class ParamFilterController {
    @Autowired
    ParamFilterDao paramFilterDao;

    @Autowired
    ValidParamCollectionDao validParamCollectionDao;

    @Autowired
    SysRoleDao sysRoleDao;

    @Autowired
    CommandDao commandDao;

    @Data
    private static class ParamFilterData {
        private String type;
        private String id;
        private String name;
        private int min;
        private int max;
        private String split;
        private String validParamCollectionId;
        private int mod;
        private int off;
        private String nextFilterId;
    }

    @RequestMapping(value = "/paramFilters", method = RequestMethod.POST)
    public Object create(@RequestBody ParamFilterData data) {
        String name = StringUtils.trimWhitespace(data.getName());
        if (StringUtils.isEmpty(name)) {
            return false;
        }
        String nextFilterId = data.getNextFilterId();
        AbstractParamFilter nextFilter = null;
        if (!StringUtils.isEmpty(nextFilterId)) {
            nextFilter = paramFilterDao.findById(nextFilterId).orElse(null);
        }
        switch (data.getType()) {
            case "RANGE":
                int min = data.getMin();
                int max = data.getMax();
                RangeParamFilter rangeParamFilter = new RangeParamFilter();
                rangeParamFilter.setName(name);
                rangeParamFilter.setMin(min);
                rangeParamFilter.setMax(max);
                paramFilterDao.save(rangeParamFilter);
                break;
            case "SPLIT":
                String split = data.getSplit();
                if (nextFilter == null)
                    return false;
                SplitParamFilter splitParamFilter = new SplitParamFilter();
                splitParamFilter.setName(name);
                splitParamFilter.setSplit(split);
                splitParamFilter.setNextFilter(nextFilter);
                paramFilterDao.save(splitParamFilter);
                break;
            case "CSV":
                String validParamCollectionId = data.getValidParamCollectionId();
                ValidParamCollection validParamCollection = validParamCollectionDao.findById(validParamCollectionId).orElse(null);
                if (validParamCollection == null)
                    return false;

                CsvParamFilter csvParamFilter = new CsvParamFilter();
                csvParamFilter.setName(name);
                csvParamFilter.setValidParamCollection(validParamCollection);
                paramFilterDao.save(csvParamFilter);
                break;
            case "MOD":
                int mod = data.getMod();
                int off = data.getOff();
                if (nextFilter == null)
                    return false;
                ModParamFilter modParamFilter = new ModParamFilter();
                modParamFilter.setName(name);
                modParamFilter.setMod(mod);
                modParamFilter.setOff(off);
                modParamFilter.setNextFilter(nextFilter);
                paramFilterDao.save(modParamFilter);
                break;
            default:
                return false;
        }
        return true;
    }

    @Data
    private static class AddData {
        private String filterId;
        private String roleId;
        private String commandName;
        private String paramName;
    }

    @RequestMapping(value = "/roles/filters", method = RequestMethod.POST)
    public Object addToRole(@RequestBody AddData data) {
        Command command = commandDao.findByName(data.getCommandName()).get();
        if(command.getParams().stream().noneMatch(param -> param.getName().equals(data.getParamName())))
            return false;
        SysRole sysRole = sysRoleDao.findById(data.getRoleId()).get();
        AbstractParamFilter filter = paramFilterDao.findById(data.getFilterId()).get();
        String commandName = data.getCommandName().replace('.', '_');
        String paramName = data.getParamName().replace('.', '_');
        SysRole.ParamWithFilters filters = sysRole.getCommandFilter().get(commandName);
        if(filters == null){
            filters = new SysRole.ParamWithFilters();
            sysRole.getCommandFilter().put(commandName, filters);
        }

        SysRole.AbstractParamFilterList list = filters.getParamFilters().get(paramName);
        if(list == null){
            list = new SysRole.AbstractParamFilterList();
            filters.getParamFilters().put(paramName, list);
        }
        list.getFilters().add(filter);
        sysRoleDao.save(sysRole);
        return true;
    }
}

