package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.File;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.rest.core.annotation.RestResource;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@RestResource
public interface FileDao extends CrudRepository<File, String> {
    @RestResource
    @Override
    Optional<File> findById(String s);
}
