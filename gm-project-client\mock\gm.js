const role_module = {
  name: 'role',
  desc: 'Role模块',
  commands: [
    {
      name: 'onlines',
      desc: '查询当前在线的玩家',
      param: [],
    },
    {
      name: 'queryRoleID',
      desc: '根据角色名称查ID',
      param: [
        {
          name: 'rolename',
          desc: '角色名称',
          type: 'string',
        },
      ],
    },
    {
      name: 'lvlup',
      desc: '提升角色达到指定等级',
      param: [
        {
          name: 'level',
          desc: '达到等级',
          type: 'int',
        },
      ],
    },
  ]
};

const cs_module = {
  name: 'Cs',
  desc: '运维及客服接口.CustomerService',
  commands: [
    {
      name: 'GetAccCharList',
      desc: '1.1\t角色列表查询',
      param: [
        {
          name: 'accid',
          desc: '用户id：userid',
          type: 'string',
        },
      ],
    },
    {
      name: 'GetChar',
      desc: '1.2\t角色信息查询',
      param: [
        {
          name: 'charid',
          desc: '角色id：roleid',
          type: 'long',
        },
        {
          name: 'charname',
          desc: '角色名称',
          type: 'string',
        },
      ],
    },
  ],
};

const servers = [{id: 1, name: "xxx"}, {id: 2, name: "yyy"}, {id: 3, name: "zzz"}];

export const getGMModules = [
  role_module,
  cs_module,
];

export const getGMServers = servers;

export default {
  getGMModules,
  getGMServers,
}
