package com.wulin.gmserver.domain.paramfilter;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RangeParamFilter extends AbstractParamFilter {
    int min;
    int max;

    @Override
    public boolean filter(Object param) {
        if (!(param instanceof String)) {
            return false;
        }
        int p = Integer.parseInt((String)param);
        if(p < min) {
            return false;
        }
        if(p > max) {
            return false;
        }
        return true;
    }
}
