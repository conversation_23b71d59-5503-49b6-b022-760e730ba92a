package com.wulin.gmserver.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonTypeResolver;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.rest.core.annotation.RestResource;

import java.util.Date;

@Data
public class User {
    @Id
    private String id;
    @Indexed(unique = true)
    @JsonProperty("name")
    private String userName;
    @JsonIgnore
    private String password;
    @JsonIgnore
    private String salt;
    private Date lastLoginTime;

    @DBRef()
    private SysRole role;
}
