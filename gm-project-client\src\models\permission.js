import { queryPermission, createPermission, editPermission, deletePermission} from '../services/permission';

export default {
  namespace: 'permission',

  state: { permissions: [] },

  effects: {
    * fetchPermission(payload, { call, put }) {
      const response = yield call(queryPermission);
      const permissions = response._embedded.sysPermissions.map(permission=>{
        let s = permission._links.self.href.split('/');
        permission.id = s[s.length - 1];
        return permission;
      });
      yield put({
        type: 'saveRole',
        payload: permissions,
      });
    },
    * createPermission({ payload }, { call, put }) {
      yield call(createPermission, payload);
      const response = yield put({ type: 'fetchPermission'});
      return response
    },
    * editPermission({ permissionId, payload }, { call, put }) {
      yield call(editPermission, permissionId, payload);
      const response = yield put({ type: 'fetchPermission'});
      return response
    },

    * deletePermission({ permissionId, payload }, { call, put }) {
      yield call(deletePermission, permissionId);
      const response = yield put({ type: 'fetchPermission'});
      return response
    },
  },

  reducers: {
    saveRole(state, action) {
      return {
        ...state,
        permissions: action.payload,
      };
    },
  },
};
