package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.Command;
import com.wulin.gmserver.domain.Param;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.rest.core.annotation.RestResource;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Repository
public interface CommandDao extends CrudRepository<Command, String> {
    Optional<Command> findByName(String name);
}