package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.Menu;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.data.rest.core.annotation.RestResource;

import java.util.Optional;

@RepositoryRestResource
public interface MenuDao extends CrudRepository<Menu, String> {

    @RestResource
    @Override
    Iterable<Menu> findAll();

//    @PreAuthorize("hasRole('Admin') and hasPermission()")
    @RestResource
    @Override
    Optional<Menu> findById(String s);
}
