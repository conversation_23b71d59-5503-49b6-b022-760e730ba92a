package com.wulin.gmserver.controller.gm;

import com.wulin.gmserver.dao.ServerDao;
import com.wulin.gmserver.domain.Command;
import com.wulin.gmserver.domain.GMModule;
import com.wulin.gmserver.domain.gm.Server;
import com.wulin.gmserver.service.GMModuleService;
import com.wulin.gmserver.xio.WgsClient;
import com.wulin.gmserver.xio.XioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import wgs.msg.gm.RequestGmCommands;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/gm/servers")
public class ServerController {
    @Autowired
    ServerDao serverDao;

    @RequestMapping(method = RequestMethod.GET)
    public Object get() {
        return serverDao.findAll();
    }

    @RequestMapping(value = "/{wgsName}", method = RequestMethod.GET)
    public Object getByWgs(@PathVariable("wgsName") String wgsName) {
        return serverDao.findAllByWgsName(wgsName);
    }

    @RequestMapping(value = "/{serverId}/updateCommands", method = RequestMethod.GET)
    public Object get(@PathVariable int serverId) {
        WgsClient.getInstance().send(WgsClient.getInstance().getCreatorByType("RELEASE"), new RequestGmCommands(serverId));
        return true;
    }
}
