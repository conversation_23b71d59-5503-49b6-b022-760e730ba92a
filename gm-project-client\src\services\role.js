import request from '../utils/request';

export async function queryRole() {
  return request('/api/sysRoles');
}

export async function createRole(params) {
  return request('/api/sysRoles', {
    method: 'POST',
    body: params,
  });
}

export async function editRole(roleId, params) {
  return request('/api/sysRoles/' + roleId, {
    method: 'PUT',
    body: params,
  });
}

export async function getRolePermissions(roleId) {
  return request('/api/sysRoles/' + roleId+"/permissions");
}

export async function updateRolePermissions(roleId, params) {
  return request('/roles/' + roleId+"/permissions", {
    method: 'POST',
    body: params,
  });
}

export async function deleteRole(roleId) {
  return request('/api/sysRoles/' + roleId, {
    method: 'DELETE',
  });
}

export async function getRole(roleId) {
  return request('/api/sysRoles/' + roleId);
}
