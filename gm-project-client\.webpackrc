{"entry": "src/index.js", "extraBabelPlugins": [["import", {"libraryName": "antd", "libraryDirectory": "es", "style": true}]], "env": {"development": {"extraBabelPlugins": ["dva-hmr"]}}, "ignoreMomentLocale": true, "theme": "./src/theme.js", "html": {"template": "./src/index.ejs"}, "publicPath": "/", "disableDynamicImport": false, "disableUglifyJs": true, "proxy": {"/": {"target": "", "changeOrigin": true, "pathRewrite": {"^/": ""}}}, "hash": true}