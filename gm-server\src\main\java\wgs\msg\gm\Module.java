
package wgs.msg.gm;

import com.goldhuman.Common.Marshal.Marshal;
import com.goldhuman.Common.Marshal.OctetsStream;
import com.goldhuman.Common.Marshal.MarshalException;

public class Module implements Marshal {
	public java.util.LinkedList<wgs.msg.gm.Command> commands;
	public java.lang.String name;
	public java.lang.String desc;

	public Module() {
		commands = new java.util.LinkedList<wgs.msg.gm.Command>();
		name = "";
		desc = "";
	}

	public Module(java.util.LinkedList<wgs.msg.gm.Command> _commands_, java.lang.String _name_, java.lang.String _desc_) {
		this.commands = _commands_;
		this.name = _name_;
		this.desc = _desc_;
	}

	public final boolean _validator_() {
		for (wgs.msg.gm.Command _v_ : commands)
			if (!_v_._validator_()) return false;
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		_os_.compact_uint32(commands.size());
		for (wgs.msg.gm.Command _v_ : commands) {
			_os_.marshal(_v_);
		}
		_os_.marshal(name, "UTF-16LE");
		_os_.marshal(desc, "UTF-16LE");
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		for (int _size_ = _os_.uncompact_uint32(); _size_ > 0; --_size_) {
			wgs.msg.gm.Command _v_ = new wgs.msg.gm.Command();
			_v_.unmarshal(_os_);
			commands.add(_v_);
		}
		name = _os_.unmarshal_String("UTF-16LE");
		desc = _os_.unmarshal_String("UTF-16LE");
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof Module) {
			Module _o_ = (Module)_o1_;
			if (!commands.equals(_o_.commands)) return false;
			if (!name.equals(_o_.name)) return false;
			if (!desc.equals(_o_.desc)) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += commands.hashCode();
		_h_ += name.hashCode();
		_h_ += desc.hashCode();
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(commands).append(",");
		_sb_.append("T").append(name.length()).append(",");
		_sb_.append("T").append(desc.length()).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

}

