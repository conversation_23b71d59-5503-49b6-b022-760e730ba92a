import React, { Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import { Button, Card, Form, Input, Modal, Divider } from 'antd';
import { Link } from 'dva/router';
import StandardTable from '../../components/StandardTable/index'; // 假设 StandardTable 组件路径正确
import PageHeaderLayout from '../../layouts/PageHeaderLayout';

const FormItem = Form.Item;

const CreateForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, item } = props; // item 未在新建时传递，但 Form.create() 的高阶组件会处理
  const okHandle = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      handleOk(fieldsValue); // 调用从 props 传来的 handleOk
      form.resetFields(); // 可选: 提交后重置表单
    });
  };
  return (
    <Modal
      title={title}
      visible={modalVisible}
      onOk={okHandle}
      onCancel={() => {
        form.resetFields(); // 可选: 取消时重置表单
        handleCancel();
      }}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="角色名称"
      >
        {form.getFieldDecorator('roleName', {
          rules: [{ required: true, message: '请输入角色名称' }],
          // initialValue: item && item.roleName, // 用于编辑，新建时 item 为空
        })(
          <Input placeholder="请输入角色名称"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="角色描述"
      >
        {form.getFieldDecorator('roleDesc', {
          rules: [{ required: true, message: '请输入角色描述' }],
          // initialValue: item && item.roleDesc, // 用于编辑，新建时 item 为空
        })(
          <Input placeholder="请输入角色描述"/>
        )}
      </FormItem>
    </Modal>
  );
});

export default @connect(({ role, loading }) => ({ // 移到最前面
  role,
  loading: loading.models.role, // 添加 loading 状态，StandardTable 可能需要
}))
@Form.create() // 第二个装饰器
class TableList extends PureComponent {
  state = {
    // modalVisible: false, // 这个状态由 this.state.modal.modalVisible 控制
    // expandForm: false, // 未见使用
    selectedRows: [],
    // formValues: {}, // 未见使用
    modal: { // 用于 CreateForm 的 props
      modalVisible: false,
      title: '',
      handleOk: () => {},
      handleCancel: () => {},
      // item: null, // 用于编辑时传递数据
    }
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'role/fetchRole',
    });
  }

  handleCreateSubmit = (data) => {
    const { dispatch } = this.props;
    // dispatch 返回的是 action 本身，dva 的 effect 如果返回 Promise，这里才能 .then
    // 假设 role/createRole effect 返回 Promise
    dispatch({
      type: 'role/createRole',
      payload: { ...data },
    }).then(() => { // .then() 接收一个回调函数
      this.setState({ // 成功后关闭模态框
        modal: {
          ...this.state.modal, // 保留其他 modal 属性，如 title, handleOk, handleCancel
          modalVisible: false,
        }
      });
      // 可以在这里重新 dispatch fetchRole 来刷新列表，如果 createRole effect 内部没有处理
      // dispatch({ type: 'role/fetchRole' });
    }).catch(err => {
      // 处理创建失败的情况，例如 Modal.error({ title: '创建失败', content: err.message });
      console.error("Create role failed:", err);
    });
  };

  handleModalCancel = () => {
    this.setState({
      modal: {
        ...this.state.modal,
        modalVisible: false,
      }
    });
  };

  handleDeleteRole = roleId => { // 参数名统一为 roleId
    const { dispatch } = this.props;
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个角色吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        dispatch({
          type: 'role/deleteRole',
          payload: { roleId: roleId } // dva effect 通常期望 payload 是一个对象
        });
      }
    });
  };

  handleSelectRows = (rows) => {
    this.setState({
      selectedRows: rows,
    });
  };

  handleModalVisible = (flag, record = null) => { // record 用于编辑，默认为 null
    this.setState({
      modal: {
        modalVisible: !!flag, // 显式转为布尔值
        handleOk: record ? this.handleUpdateSubmit : this.handleCreateSubmit, // 区分是创建还是更新
        handleCancel: this.handleModalCancel,
        title: record ? '编辑角色' : '新建角色', // 根据是否有 record 设置标题
        item: record, // 将当前行数据传给模态框，用于编辑时回显
      }
    });
  };

  // handleUpdateSubmit = (data) => { // 如果需要编辑功能
  //   const { dispatch } = this.props;
  //   const { modal } = this.state;
  //   dispatch({
  //     type: 'role/updateRole',
  //     payload: { ...modal.item, ...data }, // 合并原有 item 数据和表单数据
  //   }).then(() => {
  //     this.setState({ modal: { ...this.state.modal, modalVisible: false }});
  //   });
  // };


  render() {
    const { role: { roles }, loading } = this.props; // 从 connect 中获取 loading
    const { selectedRows, modal } = this.state;

    const defaultRoles = Array.isArray(roles) ? roles : []; // 防止 roles 为 undefined

    const columns = [
      {
        title: '角色',
        dataIndex: 'roleName',
      },
      {
        title: '角色描述',
        dataIndex: 'roleDesc',
      },
      {
        title: '操作',
        // dataIndex: 'id', // 如果用 id 作为 key，这里可以不写，render 的第二个参数 record 就包含了整行数据
        render: (text, record) => ( // record 包含了当前行所有数据，包括 id
          <Fragment>
            <a onClick={() => this.handleDeleteRole(record.id)}>删除</a>
            <Divider type="vertical" />
            <Link to={`/roles/${record.id}`}>编辑权限</Link> {/* 确保路由 /roles/:id 配置正确 */}
            {/* <Divider type="vertical" /> */}
            {/* <a onClick={() => this.handleModalVisible(true, record)}>编辑角色</a> */} {/* 示例：添加编辑角色功能 */}
          </Fragment>
        )
      },
    ];

    const parentMethods = { // 将方法传递给 CreateForm
        handleOk: modal.handleOk,
        handleCancel: modal.handleCancel,
        // item: modal.item, // item 可以在 modal 中直接传递
        // title: modal.title,
    };


    return (
      <PageHeaderLayout title="角色管理"> {/* 更改了标题 */}
        <Card bordered={false}> {/* 移除了 "advanced" */}
          <div>
            <div>
              <Button icon="plus" type="primary" onClick={() => this.handleModalVisible(true, null)}> {/* 明确传递 null 表示新建 */}
                新建角色
              </Button>
            </div>
            <StandardTable
              selectedRows={selectedRows}
              loading={loading} // 将 loading 状态传递给 StandardTable
              data={{ list: defaultRoles, pagination: false }} // 假设这里不需要分页或分页由 StandardTable 内部或 props 控制
              columns={columns}
              onSelectRow={this.handleSelectRows}
              rowKey="id" // 确保每行有唯一的 key
            />
          </div>
        </Card>
        {/* {...modal} 会把 modalVisible, title, handleOk, handleCancel, item 都传给 CreateForm */}
        {modal.modalVisible && <CreateForm {...modal} />}
      </PageHeaderLayout>
    );
  }
}