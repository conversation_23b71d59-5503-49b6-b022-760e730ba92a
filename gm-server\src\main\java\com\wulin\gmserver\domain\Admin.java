package com.wulin.gmserver.domain;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@Data
public class Admin {

    private String uid;

    @NotEmpty
    private String password;
    private String salt;
    private String username;
//    @Column(name = "create_time")
    private Date createTime;
//    @Column(name = "update_time")
    private Date updateTime;
}
