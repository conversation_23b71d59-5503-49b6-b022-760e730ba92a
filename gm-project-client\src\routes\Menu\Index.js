import React, { Component } from 'react';
import { connect } from 'dva';
import { Button, Divider, Form, Input, Modal, Tree } from 'antd';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';

const TreeNode = Tree.TreeNode;
const FormItem = Form.Item;

const CreateForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, item } = props;
  const okHandle = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      handleOk(fieldsValue);
    });
  };
  return (
    <Modal
      title={title}
      visible={modalVisible}
      onOk={okHandle}
      onCancel={() => handleCancel()}
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="名字"
      >
        {form.getFieldDecorator('name', {
          rules: [{ required: true, message: 'Please input some description...' }],
          initialValue: item && item.name
        })(
          <Input placeholder="请输入菜单名称"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="名字"
      >
        {form.getFieldDecorator('path', {
          rules: [{ required: true, message: 'Please input some description...' }],
          initialValue: item && item.path
        })(
          <Input placeholder="请输入菜单路径"/>
        )}
      </FormItem>
    </Modal>
  );
});

export default @connect(({ menu, loading }) => ({ // <--- 修改后的位置
  menu,
  loading: loading.models.rule,
}))
class TableList extends Component {
  state = {
    modalVisible: false,
    expandForm: false,
    selectedRows: [],
    formValues: {},
    modal: { // 初始化 modal 状态，避免 undefined 错误
      modalVisible: false,
      handleOk: () => {},
      handleCancel: () => {},
      item: null,
      title: ''
    }
  };

  handleEditSubmit = (menuId, data) => {
    this.props.dispatch({
      type: 'menu/updateMenu',
      payload: data,
      menuId: menuId
    });
    this.handleModalCancel(); // 关闭模态框
  };

  handleCreateSubmit = (menuId, data) => {
    this.props.dispatch({
      type: 'menu/createMenu',
      payload: {...data, parentId: menuId},
    });
    this.handleModalCancel(); // 关闭模态框
  };

  handleModalCancel = () => {
    this.setState({
      modal: {
        modalVisible: false,
        // 可选：重置其他 modal 相关的状态
        handleOk: () => {},
        handleCancel: () => {},
        item: null,
        title: ''
      }
    });
  };

  handleEditClick = item => {
    this.setState({
      modal: {
        modalVisible: true,
        handleOk: (data) => this.handleEditSubmit(item.id, data),
        handleCancel: this.handleModalCancel,
        item: item,
        title: '编辑'
      }
    });
  };

  handleDeleteClick = item => {
    // 实现删除逻辑，例如：
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除菜单 "${item.name}" 吗？其子菜单也将一并删除。`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        this.props.dispatch({
          type: 'menu/deleteMenu', // 假设你有一个 deleteMenu 的 action
          payload: { id: item.id },
        });
      },
    });
  };

  handleAddClick = item => {
    this.setState({
      modal: {
        modalVisible: true,
        handleOk: (data) => this.handleCreateSubmit(item && item.id || null, data),
        handleCancel: this.handleModalCancel,
        item: null, // 新建时 item 为 null
        title: '新建'
      }
    });
  };

  componentDidMount() {
    this.props.dispatch({
      type: 'menu/fetchMenu',
      payload: {
      },
    });
  }

  render() {
    const { menu: { menus }, loading } = this.props;
    const { modal } = this.state; // modal 现在总是有定义的

    const loop = (parentKey, data) => data.map((item, index) => {
      let key = `${parentKey}-${item.id || index}`; // 使用 item.id 保证 key 的唯一性
      return <TreeNode key={key}
        title={<div>
          {item.name}
          <Divider type="vertical"/>
          <a onClick={(e) => { e.stopPropagation(); this.handleEditClick(item);}}>编辑</a>
          <Divider type="vertical"/>
          <a onClick={(e) => { e.stopPropagation(); this.handleDeleteClick(item);}}>删除</a>
          <Divider type="vertical"/>
          <a onClick={(e) => { e.stopPropagation(); this.handleAddClick(item);}}>添加子菜单</a>
        </div>}
      >
        {(item.children && item.children.length) ? loop(key, item.children) : null}
      </TreeNode>;
    });

    return (
      <PageHeaderLayout title="菜单管理">
        <div style={{ background: '#fff', padding: 24, minHeight: 280 }}>
          <Button onClick={() => this.handleAddClick(null)} type="primary" style={{ marginBottom: 16 }}>新建根菜单</Button>
          <Tree className="draggable-tree" defaultExpandAll>
            {menus && menus.length > 0 ? loop('0', menus) : <p>暂无菜单数据，请先新建。</p>}
          </Tree>
          {modal.modalVisible && <CreateForm {...modal}/>} {/* 条件渲染 CreateForm */}
        </div>
      </PageHeaderLayout>
    );
  }
}
