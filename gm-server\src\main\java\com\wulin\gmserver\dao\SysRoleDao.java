package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.SysRole;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.data.rest.core.annotation.RestResource;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Optional;

@RepositoryRestResource
public interface SysRoleDao extends CrudRepository<SysRole, String> {
    @PreAuthorize("hasRole('Admin')")
    @RestResource
    @Override
    <S extends SysRole> S save(S entity);

    @RestResource
    @Override
    Optional<SysRole> findById(String s);

    Optional<SysRole> findByRoleName(String s);

    @RestResource
    @Override
    Iterable<SysRole> findAll();

    @PreAuthorize("hasRole('Admin')")
    @RestResource
    @Override
    void deleteById(String s);
}
