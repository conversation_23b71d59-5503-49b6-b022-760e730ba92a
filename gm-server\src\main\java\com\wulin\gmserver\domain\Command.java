package com.wulin.gmserver.domain;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;

import java.util.ArrayList;
import java.util.List;

@Data
public class Command {
    @Id
    private String id;
    @Indexed
    private String name;
    private String desc;
    private boolean withRole;
    private boolean needRecord;
    private boolean requestServer;
    private List<Param> params = new ArrayList<>();
}