package com.wulin.gmserver.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.ArrayList;
import java.util.List;

@Data
public class Menu {
    @Id
    String id;
    String name;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    String path;
    @JsonIgnore
    @DBRef
    SysPermission permission;
    @DBRef
    @JsonInclude(JsonInclude.Include.NON_NULL)
    List<Menu> children = new ArrayList<>();
}
