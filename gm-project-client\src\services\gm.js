import request from '../utils/request';
import { stringify } from 'qs';

export async function queryModules() {
  return request('/gm/modules');
}

export async function queryServers(wgsName) {
  return request('/gm/servers/' + wgsName);
}

export async function doCommand(commandName, params) {
  return request('/commands/' + commandName, {
    method: 'POST',
    body: params,
  });
}

export async function doCommandByName(params) {
  return request('/commands/callCommand', {
    method: 'POST',
    body: params,
  });
}


export async function getCommand(commandId) {
  return request('/commands/' + commandId, {
    method: 'GET',
  });
}

export async function patchCommand(commandId, params) {
  return request('/commands/' + commandId, {
    method: 'PATCH',
    body: params,
  });
}

export async function doPlayerOnlineGift(params) {
  return request('/commands/PlayerOnlineGift', {
    method: 'POST',
    body: params,
  });
}

export async function queryPlayerOnlineGiftRecord(params) {
  return request(`/commands/PlayerOnlineGift?${stringify(params)}`);
}

export async function deletePlayerOnlineGift(playerOnlineGiftId) {
  return request('/commands/PlayerOnlineGift/' + playerOnlineGiftId, {
    method: 'DELETE',
  });
}

export async function updateCommands(serverId) {
  return request('/gm/servers/' + serverId + '/updateCommands', {
    method: 'GET',
  });
}
