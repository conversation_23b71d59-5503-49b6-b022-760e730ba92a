package com.wulin.gmserver.domain.paramfilter;

import com.wulin.gmserver.domain.ValidParamCollection;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

@EqualsAndHashCode(callSuper = true)
@Data
public class CsvParamFilter extends AbstractParamFilter {
    @DBRef
    private ValidParamCollection validParamCollection;

    @Override
    public boolean filter(Object param) {
        if (!(param instanceof String)) {
            return false;
        }
        int id = Integer.parseInt((String)param);
        return validParamCollection.getValidParams().contains(id);
    }
}
