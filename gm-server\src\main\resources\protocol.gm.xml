<?xml version="1.0" encoding="utf-8"?>

<application name="wgs" shareHome="../../../share" pvids="">
    <import file="protocol.wgsd.xml" />
    
    <state name="WgsGMClient">
        <protocol ref="wgs.msg.gm.AnnounceGmCommands"/>
        <protocol ref="wgs.msg.gm.RequestGmCommands"/>
        <protocol ref="wgs.msg.gm.AnnounceServerInfo"/>
        <protocol ref="gnet.GmCmdRequest"/>
        <protocol ref="gnet.GmCmdResponse"/>
    </state>

    <service name="wgsdgmclient">
        <manager name="WgsGMClient" type="client" initstate="WgsGMClient" port="10000"/>
    </service>
</application>

