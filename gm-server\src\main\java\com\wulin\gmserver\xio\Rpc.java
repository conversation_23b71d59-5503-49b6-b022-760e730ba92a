package com.wulin.gmserver.xio;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

public class Rpc {
	private static final AtomicLong identifierGenerator = new AtomicLong();
	private static final ConcurrentHashMap<Long, Rpc> context = new ConcurrentHashMap<>();
	
	private final long identifier;
	private final xio.Protocol arg;
	private xio.Protocol result;
	
	public Rpc(long identifier, xio.Protocol arg) {
		super();
		this.identifier = identifier;
		this.arg = arg;
	}
	
	public static long nextIdentifier() {
		return identifierGenerator.incrementAndGet();
	}
	
	public long getIdentifier() {
		return identifier;
	}

	public xio.Protocol getArg() {
		return arg;
	}


	@SuppressWarnings("unchecked")
	public <Res extends xio.Protocol> Res sendAndWaitResult(xio.Xio xio, long timeoutMills)
			throws SendFailException {
		try {
			context.put(identifier, this);
			
			synchronized (this) {
				if (!this.arg.send(xio)) {
					throw new SendFailException();
				}
				
				this.wait(timeoutMills);
				return (Res)this.result;
			}
		} 
		catch (InterruptedException ex) {
			// ignore exception
		} 
		finally {
			context.remove(this.identifier);
		}
		return null;
	}
	
	public static void notifyResult(long identifier, xio.Protocol result) {
		Rpc rpc = context.get(identifier);
		if (rpc != null) {
			rpc.notifyResult(result);
		}
	}
	
	private void notifyResult(xio.Protocol result) {
		synchronized (this) {
			this.result = result;
			this.notify();
		}
	}
	
	public static class SendFailException extends Exception {
		private static final long serialVersionUID = 1L;
	} 
}
