package com.wulin.gmserver.shiro;

import org.apache.shiro.authc.credential.HashedCredentialsMatcher;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.realm.Realm;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.servlet.Cookie;
import org.apache.shiro.web.servlet.ShiroHttpSession;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.*;

//@Configuration
public class ShiroConfig {
    @Bean
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        System.out.println("ShiroConfiguration.shirFilter()");
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);

        Map<String, String> filterChainDefinitionMap = new LinkedHashMap<>();

        //配置退出过滤器,其中的具体的退出代码Shiro已经替我们实现了
        /**
         * anon（匿名）  org.apache.shiro.web.filter.authc.AnonymousFilter
         * authc（身份验证）       org.apache.shiro.web.filter.authc.FormAuthenticationFilter
         * authcBasic（http基本验证）    org.apache.shiro.web.filter.authc.BasicHttpAuthenticationFilter
         * logout（退出）        org.apache.shiro.web.filter.authc.LogoutFilter
         * noSessionCreation（不创建session） org.apache.shiro.web.filter.session.NoSessionCreationFilter
         * perms(许可验证)  org.apache.shiro.web.filter.authz.PermissionsAuthorizationFilter
         * port（端口验证）   org.apache.shiro.web.filter.authz.PortFilter
         * rest  (rest方面)  org.apache.shiro.web.filter.authz.HttpMethodPermissionFilter
         * roles（权限验证）  org.apache.shiro.web.filter.authz.RolesAuthorizationFilter
         * ssl （ssl方面）   org.apache.shiro.web.filter.authz.SslFilter
         * member （用户方面）  org.apache.shiro.web.filter.authc.UserFilter
         * user  表示用户不一定已通过认证,只要曾被Shiro记住过登录状态的用户就可以正常发起请求,比如rememberMe
         */

        //<!-- 过滤链定义，从上向下顺序执行，一般将 /**放在最为下边 -->:这是一个坑呢，一不小心代码就不好使了;
        //<!-- authc:所有url都必须认证通过才可以访问; anon:所有url都都可以匿名访问-->
//        filterChainDefinitionMap.put("/**/login", "anon");
//        filterChainDefinitionMap.put("/**/logout", "logout");
//        filterChainDefinitionMap.put("/**/reg", "anon");
//        filterChainDefinitionMap.put("/**", "authc");
        //配置记住我或认证通过可以访问的地址
//        filterChainDefinitionMap.put("/console/**", "admin");
//        filterChainDefinitionMap.put("/member/**", "custom");


        // 如果不设置默认会自动寻找Web工程根目录下的"/login.jsp"页面
//        shiroFilterFactoryBean.setLoginUrl("/member/login");
        // 登录成功后要跳转的链接
        //shiroFilterFactoryBean.setSuccessUrl("/member/index");
        //未授权界面;
        //shiroFilterFactoryBean.setUnauthorizedUrl("/console/403");
//        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        return shiroFilterFactoryBean;
    }

    @Bean(name = "customShiroRealm")
    public CustomShiroRealm customShiroRealm() {
        CustomShiroRealm customShiroRealm = new CustomShiroRealm();
//        customShiroRealm.setCacheManager(redisCacheManager());//redis权限缓存 默认缓存可注释此行
        customShiroRealm.setCredentialsMatcher(customHashedCredentialsMatcher());
        return customShiroRealm;
    }

    /**
     * 后台身份认证realm;
     *
     * @return
     */
    @Bean(name = "adminShiroRealm")
    public AdminShiroRealm adminShiroRealm() {
        AdminShiroRealm adminShiroRealm = new AdminShiroRealm();
//        adminShiroRealm.setCacheManager(redisCacheManager());//redis权限缓存 默认缓存可注释此行
        adminShiroRealm.setCredentialsMatcher(adminHashedCredentialsMatcher());
        return adminShiroRealm;
    }

    @Bean(name = "adminHashedCredentialsMatcher")
    public HashedCredentialsMatcher adminHashedCredentialsMatcher() {
        HashedCredentialsMatcher hashedCredentialsMatcher = new HashedCredentialsMatcher();
        hashedCredentialsMatcher.setHashAlgorithmName("md5");//散列算法:这里使用MD5算法;
        hashedCredentialsMatcher.setHashIterations(2);//散列的次数，当于 m比如散列两次，相d5(md5(""));
        return hashedCredentialsMatcher;
    }

    @Bean(name = "customHashedCredentialsMatcher")
    public HashedCredentialsMatcher customHashedCredentialsMatcher() {
        HashedCredentialsMatcher hashedCredentialsMatcher = new HashedCredentialsMatcher();
        hashedCredentialsMatcher.setHashAlgorithmName("md5");//散列算法:这里使用MD5算法;
        hashedCredentialsMatcher.setHashIterations(1);//散列的次数，当于 m比如散列两次，相d5("");
        return hashedCredentialsMatcher;
    }

    @Bean(name = "sessionManager")
    public DefaultWebSessionManager defaultWebSessionManager() {
        DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
        //用户信息必须是序列化格式，要不创建用户信息创建不过去，此坑很大，
//        sessionManager.setSessionDAO(redisSessionDAO());//如不想使用REDIS可注释此行
        // TODO: 2018/4/13 add session listener
//        Collection<SessionListener> sessionListeners = new ArrayList<>();
//        sessionListeners.add(customSessionListener());
//        sessionManager.setSessionListeners(sessionListeners);
        //单位为毫秒（1秒=1000毫秒） 3600000毫秒为1个小时
//        sessionManager.setSessionValidationInterval(3600000 * 12);
        //3600000 milliseconds = 1 hour
        sessionManager.setGlobalSessionTimeout(3600000 * 12);
        //是否删除无效的，默认也是开启
//        sessionManager.setDeleteInvalidSessions(true);
//        //是否开启 检测，默认开启
//        sessionManager.setSessionValidationSchedulerEnabled(true);
        //创建会话Cookie
//        Cookie cookie = new SimpleCookie(ShiroHttpSession.DEFAULT_SESSION_ID_NAME);
//        cookie.setName("WEBID");
//        cookie.setHttpOnly(true);
//        sessionManager.setSessionIdCookie(cookie);
        return sessionManager;
    }

    @Bean
    public SecurityManager securityManager() {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();

//        Map<String, Object> shiroAuthenticatorRealms = new HashMap<>();
//        shiroAuthenticatorRealms.put("adminShiroRealm", adminShiroRealm());
//        shiroAuthenticatorRealms.put("customShiroRealm", customShiroRealm());

//        Collection<Realm> shiroAuthorizerRealms = new ArrayList<>();
//        shiroAuthorizerRealms.add(adminShiroRealm());
//        shiroAuthorizerRealms.add(customShiroRealm());

//        CustomModularRealmAuthenticator customModularRealmAuthenticator = new CustomModularRealmAuthenticator();
//        customModularRealmAuthenticator.setDefinedRealms(shiroAuthenticatorRealms);
//        customModularRealmAuthenticator.setAuthenticationStrategy(authenticationStrategy());
//        securityManager.setAuthenticator(customModularRealmAuthenticator);
//        securityManager.setRealms(shiroAuthorizerRealms);
        //注入缓存管理器;
//        securityManager.setCacheManager(redisCacheManager());
        securityManager.setRealm(adminShiroRealm());
        securityManager.setSessionManager(defaultWebSessionManager());
        return securityManager;
    }
}
