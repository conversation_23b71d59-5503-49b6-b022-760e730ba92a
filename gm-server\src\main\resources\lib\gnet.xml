﻿<?xml version="1.0" encoding="utf-8"?>

<application name="gnet" shareHome="../../../share" pvids="0-10">

    <bean name="ServerLoad">
        <enum name="DOWN" value="0" comment="服务器维护"/>
        <enum name="GREEN" value="1" comment="(0-0.8)"/>
        <enum name="YELLOW" value="2" comment="(0.8-0.95)"/>
        <enum name="RED" value="3" comment="(0.95-1)"/>
        <variable name="load" type="byte"/>
    </bean>

    <bean name="ErrCode">
        <enum name="ERR_SUCCESS" value="0" comment="成功"/>
        <enum name="ERR_PASSWD_INVALID" value="1" comment="用户名或密码错误"/>
        <enum name="ERR_PASSWD_TRY_EXCEED" value="2" comment="输错密码的频率超过了deliver中的配置"/>
        <enum name="ERR_LOGINIP_TRY_EXCEED" value="3" comment="ip登陆频率超过了deliver的配置"/>
        <enum name="ERR_KICK_BY_ANOTHER_USER" value="4" comment="因账号强制登录被踢下线"/>
        <enum name="ERR_MULTILOGIN" value="5" comment="多次重复登录，且用户选择自动下线"/>
        <enum name="ERR_SERVER_NOT_CONNECT_MAPD" value="6" comment="gs没有连上Mapd"/>
        <enum name="ERR_SERVER_STOPED" value="7" comment="服务器已关闭"/>
        <enum name="ERR_SERVER_OVERLOAD" value="8" comment="服务器人数已达上限"/>
        <enum name="ERR_SERVER_ERROR" value="9" comment="服务器内部错误"/>
        <enum name="ERR_LINK_NOT_CONNECT_GS" value="10" comment="link没连上gs"/>
        <enum name="ERR_ROLEFORBID" value="11" comment="角色被GM封杀，不允许登录游戏"/>
        
        <enum name="ERR_IP_FORBID" value="12" comment="因该ip上账号或密码错误次数超限，该ip被封,时间由配置文件配置"/>
        <enum name="ERR_ACKICKOUT" value="13" comment="Kick by gs,Kick::A_ACKICKOUT"/>
        <enum name="ERR_PROTOCOL" value="14" comment="协议错误"/>
        <enum name="ERR_SERVER_NOT_IN_SERVICE" value="15" comment="系统维护中"/>
        <enum name="ERR_ACTIVATED_BUT_LOGIN_FAILED" value="16" comment="恭喜您，账号已激活成功！"/>
        <enum name="ERR_PLATFORM_FORBID" value="17" comment="玩家所在渠道被禁止登陆游戏"/>
       
        <variable name="code" type="int"/>
    </bean>

    <bean name="CDKeyErr">
        <enum name="ERR_SUCCESS" value="0" comment="成功"/>
        <enum name="ERR_FORMATE_INVALID" value="1" comment="激活码格式错误"/>
        <enum name="ERR_INVALID" value="2" comment="激活码无效"/>
        <enum name="ERR_TYPE_NOT_MATCH" value="3" comment="激活码类型不匹配"/>
        <enum name="ERR_CODE_IS_USED" value="4" comment="激活码已使用"/>
        <enum name="ERR_CODE_IS_EXPIRATED" value="5" comment="激活码已过期"/>
        <enum name="ERR_CODE_IS_NOT_OPEN" value="6" comment="激活码未到使用时间"/>
        <enum name="ERR_FUNCTION_IS_CLOSED" value="7" comment="激活码功能已关闭"/>
        <enum name="ERR_PLATFORM_NOT_MATCH" value="8" comment="激活码平台不匹配"/>
        <enum name="ERR_HAS_ALEADY_ACTIVATED" value="9" comment="已经使用过同一类型的激活码"/>
        <enum name="ERR_INTERNAL" value="10" comment="服务器内部错误"/>
        <enum name="ERR_EXCEED_DAY_USE_LIMIT" value="11" comment="超出每日使用次数"/>
        <enum name="ERR_EXCEED_ALL_USE_LIMIT" value="12" comment="超出累计使用次数"/>
        <enum name="ERR_EXCEED_CODE_USE_LIMIT" value="13" comment="超出激活码使用次数"/>
        <enum name="ERR_SERVER_NOT_MATCH" value="14" comment="激活码在当前服务器不可用"/>
        <enum name="ERR_OS_NOT_MATCH" value="15" comment="激活码在当前系统不可用"/>
        
        <variable name="code" type="int"/>
    </bean>
    
    <bean name="PlatType">
        <enum name="TEST" value="0"/>
        <enum name="ONESDK" value="1"/>
        <variable name="plat" type="int"/>
    </bean>
    
    <bean name="Forbid">
        <enum name="TYPE_LOGIN" value="1" comment="禁止登录"/>
        <enum name="TYPE_CHAT" value="2" comment="禁止聊天"/>
        
        <variable name="forbidtype" type="int" comment="禁止类型"/>
        <variable name="duration" type="int" comment="禁止时长，单位：秒"/>
        <variable name="expiredtime" type="long" comment="禁止解除时间戳，单位：毫秒"/>
        <variable name="reason" type="string" comment="封禁原因"/>
    </bean>

    <!-- client和link之间的协议 协议号范围[100,110)-->
    <protocol name="KeepAlive" type="100" maxsize="16">
        <variable name="code" type="long"/>
    </protocol>

    <protocol name="Challenge" type="101" maxsize="64">
        <variable name="version" type="int" comment="0代表不检查版本"/>
        <variable name="serverid" type="int"/>
        <variable name="serverload" type="ServerLoad"/>
    </protocol>

    <protocol name="ErrorInfo" type="102" maxsize="256">
        <variable name="errcode" type="ErrCode"/>
        <variable name="info" type="octets" attr="ref"/>
    </protocol>

    <protocol name="Response" type="103" maxsize="1024" comment="可能的返回有">
        <variable name="user_identity" type="octets"/>
        <variable name="token" type="octets"/>
        <variable name="plattype" type="PlatType" comment="在混合平台时plattype用来区分具体平台"/>
        <variable name="deviceid" type="octets" />
        <variable name="devicetoken" type="octets"/>
        <variable name="os" type="octets" />
        <variable name="platform" type="octets" />
        <variable name="media" type="octets"/>
    </protocol>
    
    <protocol name="ChallengeCDKey" type="104" maxsize="1024" comment="需要登录激活码">
        <variable name="cdkeyerr" type="gnet.CDKeyErr" comment="登录激活码验证结果，首次发给客户端时，该值是ERR_SUCCESS"/>
    </protocol>
    
    <protocol name="ResponseCDKey" type="105" maxsize="1024" comment="验证登录激活码，如果验证出错，返回ChallengeCDKey">
        <variable name="cdkey" type="string" comment="登录激活码"/>
    </protocol>

    <protocol name="KeyExchange" type="106" maxsize="32">
        <variable name="nonce" type="octets" attr="ref"/>
        <variable name="kick_olduser" type="byte" comment="客户端发给link使用这个字段"/>
    </protocol>

    <protocol name="AnnounceOnline" type="107" maxsize="64"/>
    
    <protocol name="AnnounceForbid" type="108" maxsize="10240">
        <variable name="forbid" type="Forbid"/>
    </protocol>
    
    <protocol name="AnnounceLoginQueue" type="109" maxsize="128">
        <variable name="querycount" type="int" comment="共查询的次数"/>
        <variable name="order" type="int" comment="所在登录队列的位置"/>
        <variable name="queuelength" type="int" comment="队列总长度"/>
        <variable name="foretellwaitsecond" type="int" comment="预计要等待的时间"/>
        <variable name="alreadywaitsecond" type="int" comment="已经等待的时间"/>
    </protocol>
    
    <protocol name="QueryLoginQueue" type="110" maxsize="128"/>
    
    <bean name="LatencyDebug">
        <variable name="latencyname" type="string"/>
        <variable name="sendmills" type="long"/>
        <variable name="receivemills" type="long"/>
        <variable name="processmills" type="long"/>
    </bean>
    
    <protocol name="LatencyDebugRequest" type="111" maxsize="128">
        <variable name="sequence" type="long"/>
        <variable name="clientmills" type="long"/>
        <variable name="linksid" type="long"/>
        <variable name="roleid" type="long"/>
        <variable name="latencylist" type="list" value="LatencyDebug"/>
    </protocol>
    
    <protocol name="LatencyDebugResponse" type="112" maxsize="128">
        <variable name="sequence" type="long"/>
        <variable name="clientmills" type="long"/>
        <variable name="linksid" type="long"/>
        <variable name="roleid" type="long"/>
        <variable name="latencylist" type="list" value="LatencyDebug"/>
    </protocol>
    
    <!-- END client和link之间的协议 -->
    
    <!-- 服务器之间的协议 -->
    <protocol name="GmCmdRequest" type="201" maxsize="10240">
        <variable name="identifier" type="long"/>
        <variable name="gmaccount" type="string"/>
        <variable name="roleid" type="long"/>
        <variable name="linksid" type="long"/>
        <variable name="cmdline" type="string"/>
    </protocol>

    <protocol name="GmCmdResponse" type="202" maxsize="10240">
        <variable name="identifier" type="long"/>
        <variable name="result" type="string"/>
    </protocol>
    
    <!-- link和gs之间的协议，这里的provider是一个协议组 -->
    <provider name="link" pvid="1">
        <!-- gs to link -->
        <protocol name="Bind" type="1" maxsize="1024">
            <variable name="pvid" type="int"/>
            provider id
            <variable name="linksids" type="set" value="long"/>
            bind default if empty
        </protocol>

        <protocol name="UnBind" type="2" maxsize="1024">
            <variable name="pvid" type="int"/>
            provider id
            <variable name="linksids" type="set" value="long"/>
        </protocol>

        <protocol name="Kick" type="3" maxsize="32">
            <enum name="E_PROTOCOL_UNKOWN" value="1"/>
            <enum name="E_MARSHAL_EXCEPTION" value="2"/>
            <enum name="E_PROTOCOL_EXCEPTION" value="3"/>
            <enum name="E_ACCOUNT_KICK" value="4"/>
            <enum name="E_ROLE_KICK" value="5"/>
            <enum name="E_KICK_BY_SERVER" value="6"/>
            <enum name="E_ROLE_FORBID" value="11"/>

            <enum name="A_QUICK_CLOSE" value="1"/>
            <enum name="A_DELAY_CLOSE" value="2"/>
            <enum name="A_ACKICKOUT" value="3"/>

            <variable name="linksid" type="long"/>
            <variable name="action" type="int"/>
            <variable name="error" type="int"/>
        </protocol>

        <protocol name="Multicast" type="4" maxsize="131072">
            <variable name="linksids" type="vector" value="long"/>
            <variable name="ptype" type="int"/>
            <variable name="pdata" type="octets"/>
        </protocol>
        
        <protocol name="Dispatch" type="5" maxsize="131072">
            <variable name="linksid" type="long"/>
            <variable name="ptype" type="int"/>
            <variable name="pdata" type="octets"/>
        </protocol>

        <protocol name="Broadcast" type="6" maxsize="131072">
            <variable name="ptype" type="int"/>
            <variable name="pdata" type="octets"/>
            <variable name="time" type="int"/>
        </protocol>

        <protocol name="SetLogin" type="7" maxsize="128">
            <enum name="eLogout" value="0"/>
            <enum name="eLogin" value="1"/>
            <variable name="linksid" type="long"/>
            <variable name="login" type="int"/>
            <variable name="roleid" type="long"/>
            for debug
        </protocol>
        
        <protocol name="Send" type="8" maxsize="131072">
            <variable name="linksid" type="long"/>
            <variable name="ptype" type="int"/>
            <variable name="pdata" type="octets"/>
        </protocol>

        <protocol name="LinkBroken" type="9" maxsize="32">
            <enum name="REASON_UNKNOWN" value="0" comment="未知原因"/>
            <enum name="REASON_PEER_CLOSE" value="1" comment="客户端主动断开连接"/>
            <enum name="REASON_SERVER_CLOSE" value="2" comment="服务器主动断开连接"/>
            
            <variable name="linksid" type="long"/>
            <variable name="reason" type="int"/>
        </protocol>

        <protocol name="AnnounceGsd" type="10" maxsize="32" comment="Gsd发给link，用于说明自己是Gsd，Map不要发这个协议">
            <variable name="iamgsd" type="int"/>
            <variable name="serverid" type="int"/>
            <variable name="serverload" type="gnet.ServerLoad"/>
        </protocol>
        
        <protocol name="AnnounceLinkId" type="11" maxsize="32">
            <variable name="linkid" type="int"/>
        </protocol>

        <bean name="AuthData">
            <variable name="user_identity" type="string"/>
            <variable name="token" type="string"/>
            <variable name="plattype" type="gnet.PlatType" comment="在混合平台时plattype用来区分具体平台"/>
            <variable name="deviceid" type="string" />
            <variable name="devicetoken" type="string"/>
            <variable name="os" type="string" />
            <variable name="platform" type="string" />
            <variable name="media" type="string"/>
            <variable name="loginip" type="int"/>
        </bean>
        
        <protocol name="SessionLoginRequest" type="12" maxsize="512">
            <variable name="linksid" type="long"/>
            <variable name="authdata" type="AuthData"/>
            <variable name="aftercdkey" type="bool"/>
        </protocol>

        <protocol name="SessionErrorInfo" type="13" maxsize="256">
            <variable name="linksid" type="long"/>
            <variable name="errcode" type="gnet.ErrCode"/>
            <variable name="info" type="string"/>
        </protocol>
        
        <protocol name="SessionLoginForbid" type="14" maxsize="512">
            <variable name="linksid" type="long"/>
            <variable name="forbid" type="gnet.Forbid"/>
        </protocol>
        
        <protocol name="SessionLoginResponse" type="15" maxsize="512">
            <enum name="RESULT_OK" value="0"/>
            <enum name="RESULT_ChallengeCDKey" value="1"/>
            
            <variable name="linksid" type="long"/>
            <variable name="result" type="int"/>
        </protocol>
        
        <protocol name="ActivateLoginCDKey" type="16" maxsize="512">
            <variable name="linksid" type="long"/>
            <variable name="authdata" type="AuthData"/>
            <variable name="cdkey" type="string" comment="登录激活码"/>
        </protocol>
        
        <protocol name="ActivateLoginCDKeyResponse" type="17" maxsize="512">
            <variable name="linksid" type="long"/>
            <variable name="cdkeyerr" type="gnet.CDKeyErr" comment="登录激活码验证结果"/>
        </protocol>
        
        <protocol name="SessionLoginQueueResponse" type="18" maxsize="512">
            <variable name="linksid" type="long"/>
            <variable name="querycount" type="int" comment="共查询的次数"/>
            <variable name="order" type="int" comment="所在登录队列的位置"/>
            <variable name="queuelength" type="int" comment="队列总长度"/>
            <variable name="foretellwaitsecond" type="int" comment="预计要等待的时间"/>
            <variable name="alreadywaitsecond" type="int" comment="已经等待的时间"/>
        </protocol>
        
        <protocol name="SessionLoginQueueRequest" type="19" maxsize="512">
            <variable name="linksid" type="long"/>
        </protocol>
        
        <protocol name="NotifyServerLoad" type="30" maxsize="1024">
            <variable name="serverload" type="gnet.ServerLoad"/>
        </protocol>

        <protocol name="LimitIpForAMoment" type="31" maxsize="64">
            <variable name="ip" type="int"/>
        </protocol>

        <protocol name="SetOpenAccounts" type="32" maxsize="10240">
            <variable name="accounts" type="list" value="string"/>
            <variable name="pwd" type="string"/>
        </protocol>
        
    </provider>
    <!-- END link和gs之间的协议 -->

</application>

