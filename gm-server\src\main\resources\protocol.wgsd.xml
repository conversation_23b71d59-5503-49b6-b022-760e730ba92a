<?xml version="1.0" encoding="utf-8"?>

<application name="wgs" shareHome="../../../share" pvids="11">
    <import file="lib/gnet.xml" />

    <provider name="msg" pvid="11">
        
        <protocol name="G2WAnnounceGsId" type="1" maxsize="1024" comment="gs--wgs">
            <variable name="serverid" type="int"/>
        </protocol>
        
        <namespace name="role">
            <bean name="Role">
                <variable name="serverid" type="int"/>
                <variable name="account" type="string"/>
                <variable name="roleid" type="long"/>
                <variable name="rolename" type="string"/>
                <variable name="sex" type="int"/>
                <variable name="profession" type="int"/>
                <variable name="level" type="int"/>
                <variable name="createtime" type="long"/>
            </bean>
            
            <bean name="G2WCreateRoleArg">
                <variable name="role" type="Role"/>
            </bean>
    
            <bean name="G2WCreateRoleRes">
                <enum name="ERR_SUCCESS" value="0"/>
                
                <variable name="err" type="int"/>
            </bean>
            <rpc name="G2WCreateRole" type="2" argument="G2WCreateRoleArg" result="G2WCreateRoleRes" maxsize="4096" timeout="5"/>
            
            <bean name="G2WDeleteRoleArg">
                <variable name="serverid" type="int"/>
                <variable name="account" type="string"/>
                <variable name="roleid" type="long"/>
            </bean>
    
            <bean name="G2WDeleteRoleRes">
                <enum name="ERR_SUCCESS" value="0"/>
                <enum name="ERR_USER_NOT_FOUND" value="1"/>
                <enum name="ERR_ROLE_NOT_FOUND" value="2"/>
                    
                <variable name="err" type="int"/>
            </bean>

            <rpc name="G2WDeleteRole" type="3" argument="G2WDeleteRoleArg" result="G2WDeleteRoleRes" maxsize="4096" timeout="5"/>
            
            <protocol name="G2WRoleLogin" type="4" maxsize="1024">
                <variable name="role" type="Role"/>
                <variable name="logintime" type="long"/>
            </protocol>
            
            <protocol name="G2WRoleLevelUp" type="5" maxsize="1024">
                <variable name="role" type="Role"/>
                <variable name="leveluptime" type="long"/>
            </protocol>
            
        </namespace>

        <namespace name="cdkey">

            <bean name="G2WGetCDKeyArg">
                <variable name="cdkey" type="string"/>
            </bean>

            <bean name="G2WGetCDKeyRes">
                <enum name="ERR_SUCCESS" value="0"/>
                <enum name="ERR_NOT_EXISTS" value="1"/>

                <variable name="err" type="int"/>
                <variable name="cdkeytype" type="int" comment="激活码类型"/>
                <variable name="usecount" type="int" comment="已被使用的次数"/>
                <variable name="usetime" type="long" comment="使用时间，只记录最近一次被使用时间"/>
            </bean>
            <rpc name="G2WGetCDKey" type="101" argument="G2WGetCDKeyArg" result="G2WGetCDKeyRes" maxsize="4096" timeout="5"/>

            <bean name="G2WGetCDKeyTypeArg">
                <variable name="typelist" type="list" value="int"/>
            </bean>

            <!-- 激活码类型 -->
            <bean name="CDKeyType">
                <variable name="totalusecount" type="int" comment="已被使用的次数"/>
                <variable name="createtime" type="long" comment="创建时间"/>
                <variable name="lastusetime" type="long" comment="使用时间，只记录最近一次被使用时间"/>
            </bean>

            <bean name="G2WGetCDKeyTypeRes">
                <variable name="typemap" type="map" key="int" value="CDKeyType"/>
            </bean>
            <rpc name="G2WGetCDKeyType" type="102" argument="G2WGetCDKeyTypeArg" result="G2WGetCDKeyTypeRes" maxsize="409600" timeout="5"/>

            <bean name="G2WActivateCDKeyArg">
                <variable name="account" type="string"/>
                <variable name="accountshared" type="int" comment="是否要在所有gsd上共享"/>
                <variable name="roleid" type="long"/>
                <variable name="cdkey" type="string"/>
                <variable name="expectmaxusecount" type="int" comment="期望该激活码使用次数不大于这个值"/>
                <variable name="expecttype" type="int" comment="期望该激活码的类型"/>
                <variable name="expecttypetotalmaxusecount" type="int" comment="期望[该类]激活码使用次数不大于这个值"/>
            </bean>

            <bean name="G2WActivateCDKeyRes">
                <variable name="err" type="gnet.CDKeyErr"/>
                <variable name="cdkey" type="string"/>
                <variable name="cdkeytype" type="int" comment="激活码类型"/>
                <variable name="usecount" type="int" comment="已被使用的次数"/>
                <variable name="usetime" type="long" comment="使用时间，只记录最近一次被使用时间"/>
            </bean>
            <rpc name="G2WActivateCDKey" type="103" argument="G2WActivateCDKeyArg" result="G2WActivateCDKeyRes" maxsize="4096" timeout="5"/>

            <bean name="G2WGetAccountAllCDKeyArg">
                <variable name="account" type="string"/>
            </bean>

            <bean name="G2WGetAccountAllCDKeyRes">
                <variable name="activatedmap" type="map" key="int" value="string" comment="账号已经激活的CDKey，在gsd之间共享"/>
            </bean>

            <rpc name="G2WGetAccountAllCDKey" type="105" argument="G2WGetAccountAllCDKeyArg" result="G2WGetAccountAllCDKeyRes" maxsize="40960" timeout="5"/>

            <bean name="G2WGetAccountCDKeyArg">
                <variable name="account" type="string"/>
                <variable name="cdkeytype" type="int"/>
            </bean>

            <bean name="G2WGetAccountCDKeyRes">
                <variable name="isexists" type="int"/>
                <variable name="cdkey" type="string"/>
            </bean>

            <rpc name="G2WGetAccountCDKey" type="106" argument="G2WGetAccountCDKeyArg" result="G2WGetAccountCDKeyRes" maxsize="40960" timeout="5"/>

        </namespace>

        <namespace name="data">

            <bean name="WgsKey">
                <variable name="group" type="string" comment="xdb的表名"/>
                <variable name="id" type="string" />
            </bean>

            <bean name="WgsValue">
                <variable name="value" type="int" comment="值"/>
                <variable name="serverid" type="int" comment="最后一个修改者的serverid"/>
                <variable name="roleid" type="long" comment="最后一个修改者的roleid"/>
                <variable name="time" type="long" comment="最后一个修改的时间"/>
                <variable name="version" type="int" comment="修改的版本号，每次修改都自增1"/>
            </bean>

            <bean name="G2WGetRes">
                <enum name="ERR_SUCCESS" value="0"/>
                <enum name="ERR_GROUP_NOT_EXISTS" value="1"/>
                <enum name="ERR_KEY_NOT_EXISTS" value="2"/>

                <variable name="err" type="int"/>
                <variable name="value" type="WgsValue" comment="值"/>
            </bean>
            <rpc name="G2WGet" type="201" argument="WgsKey" result="G2WGetRes" maxsize="4096" timeout="5"/>

            <bean name="G2WSetArg">
                <variable name="key" type="WgsKey" />
                <variable name="newvalue" type="int" comment="新值"/>
                <variable name="roleid" type="long" comment="最后一个修改者的roleid"/>
            </bean>

            <bean name="G2WSetRes">
                <enum name="ERR_SUCCESS" value="0"/>
                <enum name="ERR_GROUP_NOT_EXISTS" value="1"/>
                <enum name="ERR_COMPARE_FAILED" value="2"/>

                <variable name="err" type="int"/>
                <variable name="value" type="WgsValue" comment="修改后的值"/>
                <variable name="oldvalue" type="WgsValue" comment="修改之前的值"/>
            </bean>
            <rpc name="G2WSet" type="202" argument="G2WSetArg" result="G2WSetRes" maxsize="409600" timeout="5"/>

            <bean name="G2WCompareAndSetArg">
                <variable name="key" type="WgsKey" />
                <variable name="expectvalue" type="int" comment="期望的旧值"/>
                <variable name="newvalue" type="int" comment="新值"/>
                <variable name="roleid" type="long" comment="最后一个修改者的roleid"/>
            </bean>

            <bean name="G2WCompareAndSetRes">
                <enum name="ERR_SUCCESS" value="0"/>
                <enum name="ERR_GROUP_NOT_EXISTS" value="1"/>
                <enum name="ERR_COMPARE_FAILED" value="2"/>

                <variable name="err" type="int"/>
                <variable name="value" type="WgsValue" comment="修改后的值"/>
                <variable name="oldvalue" type="WgsValue" comment="修改之前的值"/>
            </bean>
            <rpc name="G2WCompareAndSet" type="203" argument="G2WCompareAndSetArg" result="G2WCompareAndSetRes" maxsize="409600" timeout="5"/>

            <bean name="G2WIncrementAndGetArg">
                <variable name="key" type="WgsKey" />
                <variable name="expectmaxvalue" type="int" comment="期望新值的最大值"/>
                <variable name="incrementvalue" type="int" comment="新曾值，必须为正数"/>
                <variable name="roleid" type="long" comment="最后一个修改者的roleid"/>
            </bean>

            <bean name="G2WIncrementAndGetRes">
                <enum name="ERR_SUCCESS" value="0"/>
                <enum name="ERR_GROUP_NOT_EXISTS" value="1"/>
                <enum name="ERR_COMPARE_FAILED" value="2"/>

                <variable name="err" type="int"/>
                <variable name="value" type="WgsValue" comment="修改后的值"/>
                <variable name="oldvalue" type="WgsValue" comment="修改之前的值"/>
            </bean>
            <rpc name="G2WIncrementAndGet" type="204" argument="G2WIncrementAndGetArg" result="G2WIncrementAndGetRes" maxsize="409600" timeout="5"/>

            <bean name="G2WDecrementAndGetArg">
                <variable name="key" type="WgsKey" />
                <variable name="expectminvalue" type="int" comment="期望新值的最小值"/>
                <variable name="decrementvalue" type="int" comment="新减值，必须为正数"/>
                <variable name="roleid" type="long" comment="最后一个修改者的roleid"/>
            </bean>

            <bean name="G2WDecrementAndGetRes">
                <enum name="ERR_SUCCESS" value="0"/>
                <enum name="ERR_GROUP_NOT_EXISTS" value="1"/>
                <enum name="ERR_COMPARE_FAILED" value="2"/>

                <variable name="err" type="int"/>
                <variable name="value" type="WgsValue" comment="修改后的值"/>
                <variable name="oldvalue" type="WgsValue" comment="修改之前的值"/>
            </bean>
            <rpc name="G2WDecrementAndGet" type="205" argument="G2WDecrementAndGetArg" result="G2WDecrementAndGetRes" maxsize="409600" timeout="5"/>
            
            <protocol name="G2WRollback" type="206" maxsize="1024">
                <variable name="key" type="WgsKey"/>
                <variable name="version" type="int" comment="回滚前Value的版本，只有在版本一致的时候才会回滚，否则直接忽略"/>
                <variable name="oldvalue" type="WgsValue" comment="回滚到以前的值"/>
            </protocol>
        </namespace>

        <namespace name="uc">
            <protocol name="W2GUcGivGift" type="301" maxsize="10240" comment="wgs --> gs  发放UC渠道礼包">
                <variable name="identifier" type="long"/>
                <variable name="useridentity" type="string" />
                <variable name="roleid" type="long"/>
                <variable name="kaid" type="int" comment="礼包id"/>
                <variable name="getDate" type="string" comment="领取日期"/>
            </protocol>
            
            <protocol name="G2WUcGiveGiftResult" type="302" maxsize="10240">
                <variable name="identifier" type="long"/>
                <variable name="err" type="int" comment="参见UcError"/>
                <variable name="msg" type="string"/>
            </protocol>
        </namespace>

        <namespace name="oppo">
            <protocol name="W2GOppoGivGift" type="401" maxsize="10240" comment="wgs --> gs  发放Oppo渠道礼包">
                <variable name="identifier" type="long"/>
                <variable name="useridentity" type="string" />
                <variable name="roleid" type="long"/>
                <variable name="giftid" type="int" comment="礼包id"/>
            </protocol>
            
            <protocol name="G2WOppoGiveGiftResult" type="402" maxsize="10240">
                <variable name="identifier" type="long"/>
                <variable name="err" type="int" comment="参见OppoError"/>
                <variable name="msg" type="string"/>
            </protocol>
        </namespace>

        <namespace name="statistic">
            <bean name="OrderStatisticsItem">
                <variable name="count" type="long" />
                <variable name="totalrmb" type="long"/>
                <variable name="totalbookrmb" type="long"/>
            </bean>

            <bean name="OrderStatistics">
                <variable name="beginmills" type="long" />
                <variable name="orders" type="map" key="int" value="OrderStatisticsItem" />
                <variable name="totalrmb" type="long"/>
                <variable name="totalbookrmb" type="long"/>
            </bean>

            <protocol name="G2WLogStatistic" type="501" maxsize="10240">
                <variable name="today" type="OrderStatistics"/>
                <variable name="total" type="OrderStatistics"/>
            </protocol>

        </namespace>
        <namespace name="huawei">
            <protocol name="W2GHuaweiGivGift" type="601" maxsize="10240" comment="wgs --> gs  发放Huawei渠道礼包">
                <variable name="identifier" type="long"/>
                <variable name="useridentity" type="string" />
                <variable name="roleid" type="long"/>
                <variable name="giftid" type="int" comment="礼包id"/>
                <variable name="requestid" type="string" comment="请求ID"/>
            </protocol>
            
            <protocol name="G2WHuaweiGiveGiftResult" type="602" maxsize="10240">
                <variable name="identifier" type="long"/>
                <variable name="err" type="int" comment="参见HuaweiError"/>
                <variable name="msg" type="string"/>
            </protocol>
        </namespace>
        <namespace name="gm">
            <bean name="Param">
                <variable name="name" type="string"/>
                <variable name="desc" type="string"/>
                <variable name="paramtype" type="string"/>
            </bean>
            <bean name="Command">
                <variable name="params" type="list" value="Param"/>
                <variable name="name" type="string"/>
                <variable name="desc" type="string"/>
                <variable name="withroleid" type="bool"/>
            </bean>
            <bean name="Module">
                <variable name="commands" type="list" value="Command"/>
                <variable name="name" type="string"/>
                <variable name="desc" type="string"/>
            </bean>
            <protocol name="AnnounceGmCommands" type="701">
                <variable name="modules" type="list" value="Module"/>
                <variable name="serverid" type="int" />
            </protocol>
            <protocol name="RequestGmCommands" type="702">
                <variable name="serverid" type="int" />
            </protocol>
            <protocol name="AnnounceServerInfo" type="703">
                <variable name="serverid" type="int"/>
                <variable name="servername" type="string"/>
                <variable name="serverstate" type="int"/>
            </protocol>
        </namespace>
    </provider>
    
    <state name="WgsServer">
        <protocol ref="gnet.KeepAlive"/>
        <provider ref="wgs.msg"/>
        <protocol ref="gnet.GmCmdRequest"/>
        <protocol ref="gnet.GmCmdResponse"/>
    </state>
    
    <state name="WgsGMServer">
        <protocol ref="wgs.msg.gm.AnnounceGmCommands"/>
        <protocol ref="wgs.msg.gm.RequestGmCommands"/>
        <protocol ref="wgs.msg.gm.AnnounceServerInfo"/>
        <protocol ref="gnet.GmCmdRequest"/>
        <protocol ref="gnet.GmCmdResponse"/>
    </state>

    <service name="wgsd">
        <manager name="WgsServer" type="server" initstate="WgsServer" port="20000"/>
        <manager name="WgsGMServer" type="server" initstate="WgsGMServer" port="20000"/>
    </service>
</application>

