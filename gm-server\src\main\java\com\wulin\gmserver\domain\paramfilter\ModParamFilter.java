package com.wulin.gmserver.domain.paramfilter;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ModParamFilter extends SequenceParamFilter {
    private int mod;
    private int off;

    @Override
    public boolean filter(Object param) {
        if (!(param instanceof Object[])) {
            return false;
        }
        Object[] params = (Object[]) param;
        int length = params.length;
        for (int i = 0; i < length; i++) {
            if (i == (length % mod) + off){
                if(!nextFilter.filter(params[i]))
                    return false;
            }
        }
        return true;
    }
}
