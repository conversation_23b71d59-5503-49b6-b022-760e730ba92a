spring.data.mongodb.uri=mongodb://localhost:27017/gm
spring.data.rest.base-path=api
spring.data.rest.detection-strategy=explicit_method_annotated
server.port=8999
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain
spring.servlet.multipart.location=${user.dir}/data/upload_tmp
spring.servlet.multipart.max-file-size=10MB
spring.quartz.job-store-type=MEMORY
#spring.resources.ca
#spring.resources.chain.gzipped=true
#spring.resources.chain.cache=false