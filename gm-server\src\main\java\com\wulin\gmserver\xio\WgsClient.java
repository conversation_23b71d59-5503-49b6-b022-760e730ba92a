package com.wulin.gmserver.xio;

import com.wulin.gmserver.util.SpringContext;
import gnet.GmCmdResponse;
import wgs.msg.gm.AnnounceGmCommands;
import wgs.msg.gm.AnnounceServerInfo;
import xio.Connector;
import xio.Creator;
import xio.Protocol;
import xio.Xio;

import java.util.HashMap;
import java.util.Map;


public class WgsClient extends xio.ManagerByCreator {
    private static volatile WgsClient instance;
    public static final String RELEASE = "WgsdConnector";
    public static final String TEST = "WgsdTestConnector";
    public static final String EXPERIENCE = "WgsdExperConnector";
    public static final Map<String, String> types = new HashMap<>();
    static {
        types.put("TEST", TEST);
        types.put("RELEASE", RELEASE);
        types.put("EXPERIENCE", EXPERIENCE);
    }
    public WgsClient() {
        if (instance != null) {
            throw new IllegalStateException("instance != null");
        }
        WgsClient.instance = this;
    }

    public static WgsClient getInstance() {
        return instance;
    }

    public boolean send(xio.Protocol p){
        Xio xio = this.get(getCreator(RELEASE));
        return p.send(xio);
    }

    public boolean send(Creator creator, xio.Protocol p){
        Xio xio = this.get(creator);
        return p.send(xio);
    }

    public Creator getCreatorByType(String type){
        return getCreator(types.get(type));
    }

    @Override
    protected void onXioAdded(xio.Xio x) {
        super.onXioAdded(x);
    }

    @Override
    public void execute(Protocol p) {
        XioService bean = SpringContext.getBean("XioService", XioService.class);
        if(p instanceof AnnounceGmCommands){
            bean.process((AnnounceGmCommands)p);
        }else if(p instanceof GmCmdResponse){
            bean.process((GmCmdResponse)p);
        }
        else if(p instanceof AnnounceServerInfo){
            bean.process((AnnounceServerInfo)p);
        }else{
        }
    }
}


