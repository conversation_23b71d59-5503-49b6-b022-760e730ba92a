.textOverflow() {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
}

.textOverflowMulti(@line: 3, @bg: #fff) {
  overflow: hidden;
  position: relative;
  line-height: 1.5em;
  max-height: @line * 1.5em;
  text-align: justify;
  margin-right: -1em;
  padding-right: 1em;
  &:before {
    background: @bg;
    content: '...';
    padding: 0 1px;
    position: absolute;
    right: 14px;
    bottom: 0;
  }
  &:after {
    background: white;
    content: '';
    margin-top: 0.2em;
    position: absolute;
    right: 14px;
    width: 1em;
    height: 1em;
  }
}

// mixins for clearfix
// ------------------------
.clearfix() {
  zoom: 1;
  &:before,
  &:after {
    content: " ";
    display: table;
  }
  &:after {
    clear: both;
    visibility: hidden;
    font-size: 0;
    height: 0;
  }
}
