package com.wulin.gmserver.quartz;

import com.wulin.gmserver.service.CommandService;
import java.util.List;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;

public class <PERSON><PERSON><PERSON> extends QuartzJobBean {

    String commandName;
    List<String> serverIds;
    List<String> params;
    

    public void setCommandName(String commandName) {
        this.commandName = commandName;
    }
    Long roleId;
    String wgs;
    String userName;
    @Autowired
    private CommandService commandService;
    
    public void setServerIds(List<String> serverIds) {
        this.serverIds = serverIds;
    }

    public void setParams(List<String> params) {
        this.params = params;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public void setWgs(String wgs) {
        this.wgs = wgs;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setCommandService(CommandService commandService) {
        this.commandService = commandService;
    }

    public void executeInternal(JobExecutionContext context) throws JobExecutionException {
        commandService.callCommandBySchedule(serverIds, commandName, params, wgs, roleId, userName);
    }
}