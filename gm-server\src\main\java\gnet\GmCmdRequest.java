
package gnet;

import com.goldhuman.Common.Marshal.OctetsStream;
import com.goldhuman.Common.Marshal.MarshalException;

// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS


/** 服务器之间的协议
*/
// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class GmCmdRequest extends xio.Protocol {
	@Override
	protected void process() {
		// protocol handle
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 201;

	public int getType() {
		return 201;
	}

	public long identifier;
	public java.lang.String gmaccount;
	public long roleid;
	public long linksid;
	public java.lang.String cmdline;

	public GmCmdRequest() {
		gmaccount = "";
		cmdline = "";
	}

	public GmCmdRequest(long _identifier_, java.lang.String _gmaccount_, long _roleid_, long _linksid_, java.lang.String _cmdline_) {
		this.identifier = _identifier_;
		this.gmaccount = _gmaccount_;
		this.roleid = _roleid_;
		this.linksid = _linksid_;
		this.cmdline = _cmdline_;
	}

	public final boolean _validator_() {
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		_os_.marshal(identifier);
		_os_.marshal(gmaccount, "UTF-16LE");
		_os_.marshal(roleid);
		_os_.marshal(linksid);
		_os_.marshal(cmdline, "UTF-16LE");
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		identifier = _os_.unmarshal_long();
		gmaccount = _os_.unmarshal_String("UTF-16LE");
		roleid = _os_.unmarshal_long();
		linksid = _os_.unmarshal_long();
		cmdline = _os_.unmarshal_String("UTF-16LE");
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof GmCmdRequest) {
			GmCmdRequest _o_ = (GmCmdRequest)_o1_;
			if (identifier != _o_.identifier) return false;
			if (!gmaccount.equals(_o_.gmaccount)) return false;
			if (roleid != _o_.roleid) return false;
			if (linksid != _o_.linksid) return false;
			if (!cmdline.equals(_o_.cmdline)) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += (int)identifier;
		_h_ += gmaccount.hashCode();
		_h_ += (int)roleid;
		_h_ += (int)linksid;
		_h_ += cmdline.hashCode();
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(identifier).append(",");
		_sb_.append("T").append(gmaccount.length()).append(",");
		_sb_.append(roleid).append(",");
		_sb_.append(linksid).append(",");
		_sb_.append("T").append(cmdline.length()).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

