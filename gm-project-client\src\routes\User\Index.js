import React, { Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import { Button, Card, Form, Input, Modal, Select } from 'antd';
import StandardTable from '../../components/StandardTable/index'; // 假设路径正确
import PageHeaderLayout from '../../layouts/PageHeaderLayout';
import moment from 'moment'; // 引入 moment 以便格式化日期，如果需要

const FormItem = Form.Item;
const { Option } = Select;

// getValue, statusMap, status 未在本组件中使用，如果 StandardTable 也不用，可以考虑移除
const getValue = obj => Object.keys(obj)
  .map(key => obj[key])
  .join(',');
const statusMap = ['default', 'processing', 'success', 'error'];
const status = ['关闭', '运行中', '已上线', '异常'];

const CreateForm = Form.create()((props) => {
  const { modalVisible, form, handleOk, handleCancel, title, roles } = props;
  const okHandle = () => {
    form.validateFields((err, fieldsValue) => {
      if (err) return;
      handleOk(fieldsValue);
      form.resetFields(); // 提交后重置表单
    });
  };

  // 确保 roles 是一个数组，避免 map of undefined 错误
  const safeRoles = Array.isArray(roles) ? roles : [];
  const roleOptions = safeRoles.map((role) => // 使用 roleOptions 替代 serverItems，更贴切
    <Option key={role.id} value={role.id}> {/* 使用 role.id 作为 key */}
      {role.roleName || role.roleDesc} {/* 优先使用 roleName，如果没有则用 roleDesc */}
    </Option>);

  return (
    <Modal
      title={title}
      visible={modalVisible}
      onOk={okHandle}
      onCancel={() => {
        form.resetFields(); // 取消时也重置表单
        handleCancel();
      }}
      destroyOnClose // 关闭时销毁 Modal 里的子元素，确保下次打开表单是干净的
    >
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="用户名"
      >
        {form.getFieldDecorator('userName', {
          rules: [{ required: true, message: '请输入新建用户名' }],
        })(
          <Input placeholder="请输入新建用户名"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }}
        label="密码"
      >
        {form.getFieldDecorator('password', {
          rules: [{ required: true, message: '请输入密码' }],
        })(
          <Input type="password" placeholder="请输入密码"/>
        )}
      </FormItem>
      <FormItem
        labelCol={{ span: 5 }}
        wrapperCol={{ span: 15 }} // wrapperCol={{ span: 50 }} 超出24栅格，改为15
        label="角色"
      >
        {form.getFieldDecorator('roleId', {
          rules: [{ required: true, message: '请选择角色' }],
        })(
          <Select
            allowClear
            style={{ width: '100%' }}
            placeholder="请选择角色"
            // filterOption removed for simplicity with antd's default behavior,
            // or ensure it works correctly: option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          >
            {roleOptions}
          </Select>
        )}
      </FormItem>
    </Modal>
  );
});

export default @connect(({ user, role, loading }) => ({ // 修正 export default 位置
  user,
  role,
  // 修正 loading 指向，例如监听 user model 的 effects
  loading: loading.models.user || loading.models.role,
}))
@Form.create()
class TableList extends PureComponent {
  state = {
    // modalVisible: false, // 由 this.state.modal.modalVisible 控制
    // expandForm: false, // 未使用
    selectedRows: [],
    // formValues: {}, // 未使用
    modal: {
      modalVisible: false,
      title: '',
      handleOk: () => {},
      handleCancel: () => {},
    }
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'role/fetchRole',
    });
    dispatch({
      type: 'user/fetchUsers',
    });
  }

  handleCreateSubmit = (data) => {
    const { dispatch } = this.props;
    // 假设 user/createUser effect 返回一个 Promise
    dispatch({
      type: 'user/createUser',
      payload: { ...data },
    }).then(() => { // 第一个 .then 接收回调函数
      this.setState(prevState => ({ // 更新 modal 状态，同时保留其他 modal 属性
        modal: {
          ...prevState.modal,
          modalVisible: false,
        }
      }));
      // 创建成功后，重新获取用户列表
      dispatch({
        type: 'user/fetchUsers',
      });
    }).catch(err => {
      // 可以添加错误处理，例如 Modal.error
      console.error("Create user failed:", err);
      Modal.error({ title: '创建失败', content: err.message || '请稍后重试' });
    });
  };

  handleModalCancel = () => {
    this.setState(prevState => ({
      modal: {
        ...prevState.modal,
        modalVisible: false,
      }
    }));
  };

  handleDeleteUser = userId => {
    const { dispatch } = this.props;
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个用户吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        // 假设 user/deleteUser effect 返回一个 Promise
        dispatch({
          type: 'user/deleteUser',
          payload: { userId: userId } // dva effect 通常期望 payload 是对象
        }).then(() => { // .then 接收回调函数
          // 删除成功后，重新获取用户列表
          dispatch({
            type: 'user/fetchUsers',
          });
        }).catch(err => {
          console.error("Delete user failed:", err);
          Modal.error({ title: '删除失败', content: err.message || '请稍后重试' });
        });
      }
    });
  };

  handleSelectRows = (rows) => {
    this.setState({
      selectedRows: rows,
    });
  };

  handleModalVisible = (isVisible, record = null) => { // record 用于编辑功能，这里是新建
    this.setState(prevState => ({
      modal: {
        ...prevState.modal,
        modalVisible: !!isVisible,
        // handleOk: record ? this.handleUpdateSubmit : this.handleCreateSubmit, // 区分创建和更新
        handleOk: this.handleCreateSubmit, // 当前只有创建
        handleCancel: this.handleModalCancel,
        title: record ? '编辑用户' : '新建用户', // 设置标题
        // item: record, // 用于编辑时传递数据
      }
    }));
  };

  render() {
    // 提供默认值，防止解构 undefined 出错
    const { user = {}, role = {}, loading } = this.props;
    const { users = [] } = user;
    const { roles = [] } = role; // CreateForm 需要 roles
    // const { roleMap } = role; // roleMap 未使用

    const { selectedRows, modal } = this.state;

    const columns = [
      {
        title: '用户',
        dataIndex: 'name',
      },
      {
        title: '角色',
        dataIndex: ['role', 'roleDesc'], // 使用路径访问嵌套对象属性
                                        // 确保 users 数据中每个 user 对象有 role: { roleDesc: 'xxx' } 结构
      },
      {
        title: '最近登入时间',
        dataIndex: 'lastLoginTime',
        sorter: (a, b) => moment(a.lastLoginTime).valueOf() - moment(b.lastLoginTime).valueOf(), // 客户端排序
        render: val => val ? <span>{moment(val).format('YYYY-MM-DD HH:mm:ss')}</span> : '-', // 格式化时间
      },
      {
        title: '操作',
        // dataIndex: 'id', // 可以不写，render 第二个参数 record 包含整行数据
        render: (text, record) => (
          <Fragment>
            <a onClick={() => this.handleDeleteUser(record.id)}>删除</a>
            {/* 可以添加编辑等其他操作 */}
          </Fragment>
        )
      },
    ];

    return (
      <PageHeaderLayout title="用户管理"> {/* 更改了标题 */}
        <Card bordered={false}> {/* 移除了 "advanced" */}
          <div>
            <div>
              <Button icon="plus" type="primary" onClick={() => this.handleModalVisible(true, null)}> {/* 明确是新建 */}
                新建用户
              </Button>
            </div>
            <StandardTable
              selectedRows={selectedRows}
              loading={loading} // 应用 loading 状态
              data={{ list: users, pagination: false }} // 假设这里不需要分页或由 StandardTable 内部处理
              columns={columns}
              onSelectRow={this.handleSelectRows}
              rowKey="id" // 确保每行有唯一的 key
            />
          </div>
        </Card>
        {/* 确保 roles 传递给 CreateForm */}
        {modal.modalVisible && <CreateForm {...modal} roles={roles} />}
      </PageHeaderLayout>
    );
  }
}