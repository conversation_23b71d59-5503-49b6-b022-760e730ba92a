package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.GMModule;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.rest.core.annotation.RestResource;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface GMModuleDao extends CrudRepository<GMModule, Integer> {
    Optional<GMModule> findByName(String name);
}
