
package gnet;

import com.goldhuman.Common.Marshal.OctetsStream;
import com.goldhuman.Common.Marshal.MarshalException;

// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS


// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class GmCmdResponse extends xio.Protocol {
	@Override
	protected void process() {
		// protocol handle
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 202;

	public int getType() {
		return 202;
	}

	public long identifier;
	public java.lang.String result;

	public GmCmdResponse() {
		result = "";
	}

	public GmCmdResponse(long _identifier_, java.lang.String _result_) {
		this.identifier = _identifier_;
		this.result = _result_;
	}

	public final boolean _validator_() {
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		_os_.marshal(identifier);
		_os_.marshal(result, "UTF-16LE");
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		identifier = _os_.unmarshal_long();
		result = _os_.unmarshal_String("UTF-16LE");
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof GmCmdResponse) {
			GmCmdResponse _o_ = (GmCmdResponse)_o1_;
			if (identifier != _o_.identifier) return false;
			if (!result.equals(_o_.result)) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += (int)identifier;
		_h_ += result.hashCode();
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(identifier).append(",");
		_sb_.append("T").append(result.length()).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

