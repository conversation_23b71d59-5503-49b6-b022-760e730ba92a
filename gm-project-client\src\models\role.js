import { queryRole, createRole, editRole, deleteRole, getRolePermissions, updateRolePermissions, getRole} from '../services/role';

export default {
  namespace: 'role',

  state: { roles: [], roleMap: new Map() },

  effects: {
    * fetchRole(payload, { call, put }) {
      const response = yield call(queryRole);
      const roleMap = new Map();
      const roles = response._embedded.sysRoles.map(role=>{
        var s = role._links.self.href.split('/');
        role.id = s[s.length - 1];
        roleMap.set(role.id, role);
        return role;
      });

      yield put({
        type: 'saveRole',
        payload: roles,
        roleMap: roleMap,
      });
    },
    * createRole({ payload }, { call, put }) {
      yield call(createRole, payload);
      const response = yield put({ type: 'fetchRole'});
    },
    * editRole({ roleId, payload }, { call, put }) {
      yield call(editRole, roleId, payload);
      const response = yield put({ type: 'fetchRole'});
    },

    * deleteRole({ roleId, payload }, { call, put }) {
      yield call(deleteRole, roleId);
      const response = yield put({ type: 'fetchRole'});
    },
    * getRolePermissions({ roleId }, { call, put }) {
      const response = yield call(getRolePermissions, roleId);
      return response
    },
    * updateRolePermissions({ roleId, payload }, { call, put }) {
      yield call(updateRolePermissions, roleId, payload);
      const response = yield put({ type: 'getRolePermissions', roleId: roleId});
      return response
    },
    * getRole({ roleId }, { call, put }) {
      const response = yield call(getRole, roleId);
      return response
    },

  },

  reducers: {
    saveRole(state, action) {
      return {
        ...state,
        roles: action.payload,
        roleMap: action.roleMap,
      };
    },
  },
};
