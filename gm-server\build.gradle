buildscript {
	ext {
		springBootVersion = '2.0.2.BUILD-SNAPSHOT'
	}
	repositories {
		mavenCentral()
		maven { url "https://repo.spring.io/snapshot" }
		maven { url "https://repo.spring.io/milestone" }
	}
	dependencies {
		classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
	}
}

apply plugin: 'java'
apply plugin: 'war'
apply plugin: 'eclipse'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'



group = 'com.wulin'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = 8

repositories {
	mavenCentral()
	maven { url "https://repo.spring.io/snapshot" }
	maven { url "https://repo.spring.io/milestone" }
}


dependencies {
//	compile("org.springframework.boot:spring-boot-starter-websocket")
	compile('org.springframework.boot:spring-boot-starter-quartz')
	compile('org.springframework.boot:spring-boot-starter-validation')
	compile('org.springframework.boot:spring-boot-starter-web')
//	compile("org.springframework.boot:spring-boot-starter-data-jpa")
	compile('org.springframework.boot:spring-boot-starter-data-mongodb')
	compile("org.springframework.boot:spring-boot-starter-data-rest")
	compile('org.springframework.boot:spring-boot-starter-security')
//	compile('org.springframework.boot:spring-boot-starter-actuator')
	compile("org.springframework.data:spring-data-rest-hal-browser")

	compile 'org.apache.commons:commons-csv:1.8'

	compileOnly 'org.projectlombok:lombok:1.16.20'

	compile files('lib/xdb.jar')
	testCompile('org.springframework.boot:spring-boot-starter-test')
}
