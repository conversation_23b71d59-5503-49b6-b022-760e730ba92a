package com.wulin.gmserver.domain.paramfilter;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SplitParamFilter extends SequenceParamFilter {
    private String split;

    @Override
    public boolean filter(Object param) {
        if (!(param instanceof String)) {
            return false;
        }
        String str = (String) param;
        Object[] params = str.split(split);
        return nextFilter.filter(params);
    }
}
