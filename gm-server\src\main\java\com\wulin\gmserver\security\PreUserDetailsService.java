package com.wulin.gmserver.security;

import com.wulin.gmserver.dao.UserDao;
import com.wulin.gmserver.domain.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.AuthenticationUserDetailsService;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.preauth.PreAuthenticatedAuthenticationToken;
import org.springframework.stereotype.Component;

import java.util.Date;


@Component
public class PreUserDetailsService implements AuthenticationUserDetailsService<PreAuthenticatedAuthenticationToken> {
    @Override
    public UserDetails loadUserDetails(PreAuthenticatedAuthenticationToken token) throws UsernameNotFoundException {
        String username = (String)token.getPrincipal();
        User user = userDao.findByUserName(username).orElse(null);
        if (user == null) {
            throw new UsernameNotFoundException(username);
        }
        user.setLastLoginTime(new Date());
        userDao.save(user);
        return new MyUserDetails(user);
    }

    @Autowired
    private UserDao userDao;
}
