package com.wulin.gmserver.security;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.security.MessageDigest;

public class Constant {

//	wanmei_sign_secrets["wlgm.sys.wanmei.net"] = "aaoHk1yUPPcOGQno9w1A"
	public static final String SSO_KEY = "aaoHk1yUPPcOGQno9w1A";
//	public static final String SSO_KEY = "1wqwsusdyn43235ghb75d5aoi";
	public static final String SESSION_KEY = "USER";

	/**
	 * 二进制转换成16进制
	 *
	 * @param data
	 * @return
	 */
	public static String convertToHexString(byte data[]) {
		StringBuffer strBuffer = new StringBuffer();

		char[] dict = new char[] { '0', '1', '2', '3', '4', '5', '6', '7', '8',
				'9', 'a', 'b', 'c', 'd', 'e', 'f' };

		for (int i = 0; i < data.length; i++) {
			strBuffer.append(dict[(data[i] >> 4) & 0xf]);
			strBuffer.append(dict[(data[i]) & 0xf]);
		}
		return strBuffer.toString();
	}

	/*
	 *
	 * 生成http请求签名
	 */
	public static String gen_http_sign(String uri, String xssouid,
									   String remoteip, String user_agent, String token_secret) {
		String strmd5 = "";
		strmd5 += uri;
		strmd5 += xssouid;
		strmd5 += remoteip;
		strmd5 += user_agent;
		strmd5 += token_secret;
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			md.update(strmd5.getBytes());
			String ret=convertToHexString(md.digest());
//			System.out.println("gen_http_sign:strmd5="+strmd5+",ret="+ret);
			return ret;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public static void Copy(String oldPath, String newPath) {
		try {
			int bytesum = 0;
			int byteread = 0;
			File oldfile = new File(oldPath);
			if (oldfile.exists()) {
				InputStream inStream = new FileInputStream(oldPath);
				FileOutputStream fs = new FileOutputStream(newPath);
				byte[] buffer = new byte[1444];
				while ((byteread = inStream.read(buffer)) != -1) {
					bytesum += byteread;
					fs.write(buffer, 0, byteread);
				}
				inStream.close();
				fs.close();
			}
		} catch (Exception e) {
			System.out.println("error  ");
			e.printStackTrace();
		}
	}
}
