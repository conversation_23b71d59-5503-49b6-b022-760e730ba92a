package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.paramfilter.AbstractParamFilter;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.data.rest.core.annotation.RestResource;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Optional;

@RepositoryRestResource(path = "paramFilters")
public interface ParamFilterDao extends CrudRepository<AbstractParamFilter, String> {
    @RestResource
    @Override
    Optional<AbstractParamFilter> findById(String s);

    @RestResource
    @Override
    Iterable<AbstractParamFilter> findAll();

    @PreAuthorize("hasRole('Admin')")
    @RestResource
    @Override
    void deleteById(String s);
}
