package com.wulin.gmserver.util;

import com.fasterxml.jackson.databind.ObjectMapper;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSession;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

public class HttpUtils {
    public static String callUrl(String url, Map<String, String> data) throws Exception {

        HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
            public boolean verify(String urlHostName, SSLSession session) {
                return true;
            }
        });
        URL u = new URL(url);
        HttpURLConnection conn = (HttpURLConnection)u.openConnection();
        String ret = "";
        try {
            conn.setAllowUserInteraction(false);
            conn.setUseCaches(false);
            conn.setRequestProperty("User-Agent", "wanmei/wulin");
            conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(5000);

            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setDoInput(true);

//            mapper
            String s = data.entrySet().stream().map(e->e.getKey() + "=" +e.getValue()).reduce((a,b)->a+"&"+b).orElse("");
            conn.getOutputStream().write(s.toString().getBytes("UTF-8"));
            conn.getOutputStream().flush();
            conn.getOutputStream().close();

            int linenum = 0;
            try (BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()))) {
                String temp;
                while ((temp = br.readLine()) != null) {
                    if (linenum++ >= 10240) {
                        throw new IllegalStateException();
                    }
                    ret += temp;
                }
            }
        }
        finally {
            conn.disconnect();
        }
        return ret;
    }
}
