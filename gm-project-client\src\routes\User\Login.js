import React, { Component } from 'react';
import { connect } from 'dva';
import { Link } from 'dva/router';
import { Alert, Checkbox, Icon } from 'antd';
import Login from '../../components/Login'; // 假设 Login 组件路径正确
import styles from './Login.less'; // 假设样式文件路径正确

const { Tab, UserName, Password, Mobile, Captcha, Submit } = Login;

export default @connect(({ login, loading }) => ({ // <--- `export default` 移到最前面
  login,
  submitting: loading.effects['login/login'], // loading.effects 指向特定 effect 的加载状态
}))
class LoginPage extends Component {
  state = {
    type: 'account', // 'account' 或 'mobile'
    autoLogin: true,
  }

  onTabChange = (type) => {
    this.setState({ type });
  }

  handleSubmit = (err, values) => {
    const { type } = this.state; // type from state is not used in dispatch payload in original code
    if (!err) {
      this.props.dispatch({
        type: 'login/login',
        payload: {
          ...values,
          // type, // 如果 login effect 需要知道登录类型，可以加上
        },
      });
    }
  }

  changeAutoLogin = (e) => {
    this.setState({
      autoLogin: e.target.checked,
    });
  }

  renderMessage = (content) => {
    return (
      <Alert style={{ marginBottom: 24 }} message={content} type="error" showIcon />
    );
  }

  render() {
    const { login, submitting } = this.props;
    const { type, autoLogin } = this.state; // autoLogin from state

    return (
      <div className={styles.main}>
        <Login
          defaultActiveKey={type}
          onTabChange={this.onTabChange}
          onSubmit={this.handleSubmit}
        >
          <Tab key="account" tab="账户密码登录">
            {
              login && login.status === 'error' && // 检查 login 对象是否存在
              login.type === 'account' &&
              !submitting && // 使用从 props 传递的 submitting 状态
              this.renderMessage(login.message || '账户或密码错误') // 优先使用后端返回的 message
            }
            <UserName name="username" placeholder="admin 或 user" /> {/* 示例 placeholder */}
            <Password name="password" placeholder="任意密码" /> {/* 示例 placeholder */}
          </Tab>
          {/* 手机号登录的 Tab 被注释掉了，保持不变 */}
          {/* <Tab key="mobile" tab="手机号登录">
            {
              login && login.status === 'error' &&
              login.type === 'mobile' &&
              !submitting &&
              this.renderMessage(login.message || '验证码错误')
            }
            <Mobile name="mobile" />
            <Captcha name="captcha" />
          </Tab> */}
          <div>
            <Checkbox checked={autoLogin} onChange={this.changeAutoLogin}>自动登录</Checkbox>
            <a style={{ float: 'right' }} href="">忘记密码</a> {/* href="" 不是一个有效的链接目标 */}
          </div>
          <Submit loading={submitting}>登录</Submit>
          <div className={styles.other}>
            其他登录方式
            <Icon className={styles.icon} type="alipay-circle" />
            <Icon className={styles.icon} type="taobao-circle" />
            <Icon className={styles.icon} type="weibo-circle" />
            <Link className={styles.register} to="/user/register">注册账户</Link>
          </div>
        </Login>
      </div>
    );
  }
}