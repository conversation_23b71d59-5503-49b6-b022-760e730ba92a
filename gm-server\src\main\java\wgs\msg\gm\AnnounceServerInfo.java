
package wgs.msg.gm;

import com.goldhuman.Common.Marshal.OctetsStream;
import com.goldhuman.Common.Marshal.MarshalException;

// {{{ RPCGEN_IMPORT_BEGIN
// {{{ DO NOT EDIT THIS


// DO NOT EDIT THIS }}}
// RPCGEN_IMPORT_END }}}

public class AnnounceServerInfo extends xio.Protocol {
	@Override
	protected void process() {
		// protocol handle
	}

	// {{{ RPCGEN_DEFINE_BEGIN
	// {{{ DO NOT EDIT THIS
	public static final int PROTOCOL_TYPE = 721599;

	public int getType() {
		return 721599;
	}

	public int serverid;
	public java.lang.String servername;
	public int serverstate;

	public AnnounceServerInfo() {
		servername = "";
	}

	public AnnounceServerInfo(int _serverid_, java.lang.String _servername_, int _serverstate_) {
		this.serverid = _serverid_;
		this.servername = _servername_;
		this.serverstate = _serverstate_;
	}

	public final boolean _validator_() {
		return true;
	}

	public OctetsStream marshal(OctetsStream _os_) {
		_os_.marshal(serverid);
		_os_.marshal(servername, "UTF-16LE");
		_os_.marshal(serverstate);
		return _os_;
	}

	public OctetsStream unmarshal(OctetsStream _os_) throws MarshalException {
		serverid = _os_.unmarshal_int();
		servername = _os_.unmarshal_String("UTF-16LE");
		serverstate = _os_.unmarshal_int();
		return _os_;
	}

	public boolean equals(Object _o1_) {
		if (_o1_ == this) return true;
		if (_o1_ instanceof AnnounceServerInfo) {
			AnnounceServerInfo _o_ = (AnnounceServerInfo)_o1_;
			if (serverid != _o_.serverid) return false;
			if (!servername.equals(_o_.servername)) return false;
			if (serverstate != _o_.serverstate) return false;
			return true;
		}
		return false;
	}

	public int hashCode() {
		int _h_ = 0;
		_h_ += serverid;
		_h_ += servername.hashCode();
		_h_ += serverstate;
		return _h_;
	}

	public String toString() {
		StringBuilder _sb_ = new StringBuilder();
		_sb_.append("(");
		_sb_.append(serverid).append(",");
		_sb_.append("T").append(servername.length()).append(",");
		_sb_.append(serverstate).append(",");
		_sb_.append(")");
		return _sb_.toString();
	}

	// DO NOT EDIT THIS }}}
	// RPCGEN_DEFINE_END }}}

}

