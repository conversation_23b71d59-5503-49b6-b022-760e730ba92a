import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Card, Table } from 'antd';
import PageHeaderLayout from '../../layouts/PageHeaderLayout';
import moment from 'moment'; // 假设时间字段需要格式化，如果不需要 moment 可以移除

const getValue = obj => Object.keys(obj)
  .map(key => obj[key])
  .join(',');

// 这两个变量在当前代码片段中未被使用，如果确实不需要可以移除
// const statusMap = ['default', 'processing', 'success', 'error'];
// const status = ['关闭', '运行中', '已上线', '异常'];

// 将 columns 定义移到 class 外部是常见的做法，但如果列定义依赖于 this (例如事件处理器)，则需放在 render 方法内或 class 内部
const mainColumns = [ // 重命名以避免与 expandedRowRender 内的 columns 混淆
  {
    title: '用户',
    dataIndex: ['owner', 'name'], // 使用数组路径更安全
    key: 'username', // key 通常与 dataIndex 一致或唯一标识
  },
  {
    title: '时间',
    dataIndex: 'time',
    key: 'time',
    sorter: true,
    render: (text) => text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-', // 可选：格式化时间
  },
  {
    title: '命令',
    dataIndex: 'command',
    key: 'cmd', // key 通常与 dataIndex 一致或唯一标识
  },
];

export default @connect(({ record, loading }) => ({ // <--- 修改后的位置
  record,
  loading: loading.models.record, // 建议：通常 loading 状态会对应 model 名称，例如 loading.models.record
}))
class TableList extends PureComponent {
  state = {
    // modalVisible: false, // 未使用
    // expandForm: false, // 未使用
    // selectedRows: [], // 未使用
    formValues: {}, // 用于存储筛选条件等
  };

  componentDidMount() {
    const { dispatch } = this.props;
    dispatch({
      type: 'record/fetchRecord',
      // payload: { currentPage: 1, pageSize: 10 } // 初始加载时可以带上默认分页参数
    });
  }

  handleStandardTableChange = (pagination, filtersArg, sorter) => {
    const { dispatch } = this.props;
    const { formValues } = this.state;

    const filters = Object.keys(filtersArg)
      .reduce((obj, key) => {
        const newObj = { ...obj };
        newObj[key] = getValue(filtersArg[key]);
        return newObj;
      }, {});

    const params = {
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
      ...formValues,
      ...filters,
    };

    if (sorter.field && sorter.order) { // 确保 sorter.order 存在
      params.sorter = `${sorter.field}_${sorter.order === 'ascend' ? 'asc' : 'desc'}`;
    }

    dispatch({
      type: 'record/fetchRecord',
      payload: params,
    });
  };

  renderExpandedRowContent = item => { // 将方法名写得更清晰，并作为类方法
    const expandedData = item.msg && Array.isArray(item.msg) ? item.msg : []; // 确保 item.msg 是数组
    const expandedColumns = [
      { title: '服务器ID', dataIndex: 'serverId', key: 'serverId', width: 100 },
      { title: '执行结果', dataIndex: 'result', key: 'result' },
    ];
    return (
      <Table
        columns={expandedColumns}
        dataSource={expandedData}
        pagination={false}
        rowKey={(record, index) => record.serverId || `expanded-${index}`} // 为展开行提供唯一的 key
      />
    );
  };

  render() {
    const { record: { data }, loading } = this.props; // 从 props 中获取 loading

    // 提供默认值，防止 data, data.list, data.pagination 为 undefined 时报错
    const listData = data && data.list ? data.list : [];
    const paginationProps = data && data.pagination ? {
      showSizeChanger: true,
      showQuickJumper: true,
      ...data.pagination,
      current: data.pagination.current || data.pagination.currentPage, // 兼容 currentPage
    } : {
      current: 1,
      pageSize: 10,
      total: 0,
    };

    return (
      <PageHeaderLayout title="用户操作记录"> {/* 统一标题 */}
        <Card bordered={false}> {/* PageHeaderLayout 已有标题，Card 的 title 可移除 */}
          <div>
            <Table
              rowKey={record => record.id || record.key} // 确保每行有唯一的 key, 优先使用 'id'
              loading={loading} // 应用 loading 状态
              dataSource={listData}
              columns={mainColumns} // 使用重命名后的列定义
              pagination={paginationProps}
              onChange={this.handleStandardTableChange}
              expandedRowRender={this.renderExpandedRowContent} // 使用类方法
            />
          </div>
        </Card>
      </PageHeaderLayout>
    );
  }
}