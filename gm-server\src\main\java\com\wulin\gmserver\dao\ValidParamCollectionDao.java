package com.wulin.gmserver.dao;

import com.wulin.gmserver.domain.ValidParamCollection;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.rest.core.annotation.RepositoryRestResource;
import org.springframework.data.rest.core.annotation.RestResource;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Optional;

@RepositoryRestResource
public interface ValidParamCollectionDao extends CrudRepository<ValidParamCollection, String> {
    @PreAuthorize("hasRole('Admin')")
    @RestResource
    @Override
    <S extends ValidParamCollection> S save(S entity);

    @RestResource
    @Override
    Optional<ValidParamCollection> findById(String s);

    @RestResource
    @Override
    Iterable<ValidParamCollection> findAll();

    @PreAuthorize("hasRole('Admin')")
    @RestResource
    @Override
    void deleteById(String s);
}
