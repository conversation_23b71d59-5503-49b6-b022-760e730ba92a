import React, { Component } from 'react';
import { <PERSON><PERSON>, Card, Col, Form, Row, Select } from 'antd';
import { connect } from 'dva';

@connect(({ global, gm, loading }) => ({
  collapsed: global.collapsed,
  submitting: loading.effects['gm/doCommandByName'],
  gm: gm,
}))
@Form.create()
class GMForm extends Component {
  state = {
    selectedSeverIds: []
  };

  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFields((err, values) => {
      if (!err) {
        let servers = this.props.gm.servers;
        let serverIds = values.servers.map(serverId => {
          let s = servers.find(server => server.serverId === serverId);
          return s && s.id
        });
        this.props.dispatch({
          type: 'gm/doCommandByName',
          payload: {
            serverIds: serverIds,
            commandName: 'xdb.get',
            params: ['0', 'globalusers'],
          },
        }).then(this.handleUserCountCallBack).then(()=>this.props.dispatch({
          type: 'gm/doCommandByName',
          payload: {
            serverIds: serverIds,
            commandName: 'loginqueue.listnum',
            params: [],
          },
        }).then(this.handleStateCallBack));

        this.setState({
          userCountResult: null,
          loginQueueResult: null,
        });
      }
    });
  };


  handleStateCallBack = data => {
    this.setState({
      loginQueueResult: data
    });
  };

  handleUserCountCallBack = data => {
    this.setState({
      userCountResult: data
    });
  };

  handleSelectAllServers = () => {
    const { gm: { servers }, form } = this.props;
    form.setFieldsValue({ servers: servers.map(server=>server.serverId) });
  };

  componentDidMount() {
    this.props.dispatch({
      type: 'gm/fetchServers',
    });
  }

  render() {
    const { getFieldDecorator } = this.props.form;
    const { submitting, gm: { servers } } = this.props;

    const userCountResult = this.state.userCountResult;
    const loginQueueResult = this.state.loginQueueResult;
    const data = loginQueueResult !== undefined && loginQueueResult !== null ? loginQueueResult.map(a=>{
      let result = a.result
      let ls = userCountResult ? userCountResult.find(l=>l.serverId === a.serverId) : null;
      if(ls){
        result = a.result + "\r\n" +ls.result
      }
      return {serverId: a.serverId, result: result}
    }): null;
    const renderCommandResult = data !== null ? data.sort((a,b)=>a.serverId - b.serverId).map(a => (
      <Row gutter={16} key={a.serverId}>
        <Col span={6}>{a.serverId}</Col>
        <Col span={18}>
          <pre>{a.result}</pre>
        </Col>
      </Row>
    )) : null;

    const serverItems = servers.sort((a,b)=>a.serverId - b.serverId).map((server, index) =>
      <Select.Option key={index} value={server.serverId}>
        {server.name.length === 0 ? '服务器' + server.serverId : server.name + '('+server.serverId+')'}
      </Select.Option>);

    const ServerSelect = (
      <Form.Item>
        {getFieldDecorator('servers', {
          rules: [{ required: true, message: '请选择服务器' }],
        })(
          <Select style={{ maxWidth: 1000, width: '100%' }} mode={'multiple'} allowClear={true}
                  placeholder="请选择服务器(可多选)"
                  filterOption={(input, option) => option.props.value.toString().indexOf(input) === 0}
          >
            {serverItems}
          </Select>
        )}
        <Button onClick={this.handleSelectAllServers}>全选</Button>
      </Form.Item>
    );

    return (
      <>
        <Card title={"排队查询"} bordered={false}>
          <Form onSubmit={this.handleSubmit}>
            <Card bordered={false} bodyStyle={{ padding: 0 }}>
              {ServerSelect}
            </Card>

            <Form.Item>
              <Button type='primary' htmlType='submit' loading={submitting}>
                提交
              </Button>
            </Form.Item>
          </Form>
        </Card>

        <Card title="执行结果" bordered={false}>
          <div>
            {renderCommandResult}
          </div>
        </Card>
      </>
    );

  };
}

export default GMForm;
