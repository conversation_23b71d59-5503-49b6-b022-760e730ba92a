package com.wulin.gmserver.xio;

import com.wulin.gmserver.dao.ServerDao;
import com.wulin.gmserver.domain.Command;
import com.wulin.gmserver.domain.GMModule;
import com.wulin.gmserver.domain.Param;
import com.wulin.gmserver.domain.gm.Server;
import com.wulin.gmserver.service.GMModuleService;
import gnet.GmCmdResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import wgs.msg.gm.AnnounceGmCommands;
import wgs.msg.gm.AnnounceServerInfo;
import wgs.msg.gm.Module;

import java.util.stream.Collectors;

@Service("XioService")
public class XioService {
    @Autowired
    GMModuleService gmModuleService;

    @Autowired
    ServerDao serverDao;

    public void process(AnnounceGmCommands proto) {
        for (Module module : proto.modules) {
            GMModule gmModule = new GMModule();
            gmModule.setName(module.name.replace('.', '_'));
            gmModule.setDesc(module.desc);
            gmModule.setCommands(
                    module.commands.stream().map(c -> {
                        Command command = new Command();
                        command.setDesc(c.desc);
                        command.setName(c.name.replace('.', '_'));
                        command.setWithRole(c.withroleid);

                        command.setParams(
                                c.params.stream().map(p -> {
                                    Param param = new Param();
                                    param.setName(p.name);
                                    param.setDesc(p.desc);
                                    param.setParamtype(p.paramtype);
                                    return param;
                                }).collect(Collectors.toList())
                        );
                        return command;
                    }).collect(Collectors.toList())
            );
            gmModuleService.createOrUpdate(gmModule);
        }
    }

    public void process(GmCmdResponse p) {
        Rpc.notifyResult(p.identifier, p);
    }

    public void process(AnnounceServerInfo p) {
        String creatorName = p.getConnection().getCreator().getName();
        String wgsName = "";
        if(creatorName.equals(WgsClient.RELEASE))
            wgsName = "RELEASE";
        else if(creatorName.equals(WgsClient.TEST))
            wgsName = "TEST";
        else if(creatorName.equals(WgsClient.EXPERIENCE))
            wgsName = "EXPERIENCE";

//        String wgsName = .equals(WgsClient.RELEASE) ? "RELEASE" : "TEST";
        final String wgsType = wgsName;
        Server server = serverDao.findByServerIdAndWgsName(p.serverid, wgsName).orElseGet(()->{
            Server s = new Server();
            s.setServerId(p.serverid);
            s.setWgsName(wgsType);
            return s;
        });
        server.setName(p.servername);
        serverDao.save(server);
    }
}
